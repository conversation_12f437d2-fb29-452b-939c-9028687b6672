import os 
from pathlib import Path 
from tqdm import tqdm 
import pandas as pd 
import re 
from collections import defaultdict 
 
from tree_sitter import Language, Parser 
import tree_sitter_java as tsjava 
 
from langchain_community.document_loaders import TextLoader 
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language 
from langchain_google_genai import ChatGoogleGenerativeAI 
from langchain_experimental.graph_transformers import LLMGraphTransformer 
from langchain_community.graphs import Neo4jGraph 
 
BASE_PATH = Path(r "C:/Shaik/sample/GihubActions ") 
NEO4J_URI =  "bolt://localhost:7687 " 
NEO4J_USER =  "neo4j " 
NEO4J_PASSWORD =  "Test@7889 " 
NEO4J_DB =  "githubactions2 " 
GOOGLE_API_KEY =  "AIzaSyASelgh7zLlDIpbpNyJkgkimlM_ODU86dY " 
 
graph = Neo4jGraph(url=NEO4J_URI, username=<PERSON>O4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB) 
JAVA_LANGUAGE = Language(tsjava.language()) 
parser = Parser(JAVA_LANGUAGE) 
 
llm = ChatGoogleGenerativeAI( 
  model= "gemini-2.5-pro ", 
  temperature=0, 
  google_api_key=GOOGLE_API_KEY 
) 
 
# Node colors configuration 
NODE_COLORS = { 
  'folder': '#FFB6C1',  # Light Pink 
  'file': '#87CEEB',   # Sky Blue 
  'class': '#98FB98',   # Pale Green 
  'interface': '#DDA0DD', # Plum 
  'method': '#F0E68C',  # Khaki 
  'variable': '#FFA07A', # Light Salmon 
  'endpoint': '#20B2AA', # Light Sea Green 
  'table': '#D2691E'   # Chocolate 
} 
 
# Relationship colors configuration 
RELATIONSHIP_COLORS = { 
  'contains': '#4169E1',   # Royal Blue 
  'declares': '#32CD32',   # Lime Green 
  'has_field': '#FF6347',  # Tomato 
  'uses': '#9370DB',     # Medium Purple 
  'calls': '#FF1493',    # Deep Pink 
  'extends': '#00CED1',   # Dark Turquoise 
  'implements': '#FF8C00',  # Dark Orange 
  'maps_to': '#8B4513',   # Saddle Brown 
  'reads_from': '#2E8B57',  # Sea Green 
  'writes_to': '#B22222'   # Fire Brick 
} 
 
# Common temp variables to filter out 
TEMP_VARIABLES = { 
  'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z', 
  'temp', 'tmp', 'temporary', 'temp1', 'temp2', 
  'count', 'counter', 'index', 'idx', 'iter', 
  'result', 'res', 'ret', 'val', 'value', 
  'item', 'elem', 'element', 'obj', 'object', 
  'str', 'string', 'num', 'number', 'flag', 
  'bool', 'boolean', 'arr', 'array', 'list', 
  'map', 'set', 'data', 'info', 'param', 'arg' 
} 
 
def to_pascal_case(text): 
   " " "Convert text to PascalCase " " " 
  if not text: 
    return text 
  # Remove file extensions first 
  text = re.sub(r'  .(java|class)$', '', text, flags=re.IGNORECASE) 
  # Split on various delimiters and capitalize each part 
  parts = re.split(r'[_  -  s  .]+', text) 
  return ''.join(word.capitalize() for word in parts if word) 
 
def is_temp_variable(var_name): 
   " " "Check if variable is a common temp variable " " " 
  if not var_name: 
    return True 
  var_lower = var_name.lower().strip() 
  return var_lower in TEMP_VARIABLES or len(var_lower) <= 1


# ========== STAGE 1: FOLDER + FILE HIERARCHY ==========
def extract_folder_file_hierarchy(base_path):
  folder_records, file_records = [], []
  base_path = os.path.abspath(base_path)
  base_folder_name = os.path.basename(base_path)
  processed_folders = set()

  for root, dirs, files in os.walk(base_path):
    rel_root = os.path.relpath(root, base_path)
    parent_folder = base_folder_name if rel_root == '.' else os.path.dirname(rel_root) or base_folder_name
    current_folder = base_folder_name if rel_root == '.' else os.path.basename(rel_root)

    folder_key = f'{parent_folder}->{current_folder}'
    if folder_key not in processed_folders and parent_folder != current_folder:
      folder_records.append({
        'source_node': parent_folder,
        'source_type': 'folder',
        'destination_node': current_folder,
        'destination_type': 'folder',
        'relationship': 'contains',
        'file_path': None
      })
      processed_folders.add(folder_key)

    for f in files:
      if f.endswith('.java'):
        file_rel_path = os.path.relpath(os.path.join(root, f), base_path)
        file_records.append({
          'source_node': current_folder,
          'source_type': 'folder',
          'destination_node': f,
          'destination_type': 'file',
          'relationship': 'contains',
          'file_path': file_rel_path
        })
  return folder_records, file_records

folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)
df_folders = pd.DataFrame(folder_records)
df_files = pd.DataFrame(file_records)

print(f'Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships')


# ========== UTILITY FUNCTIONS ==========
def read_source_code(file_path):
  with open(file_path, 'r', encoding='utf-8') as f:
    return f.read().encode('utf-8')

def extract_package_and_imports(source_code_str):
  package_pattern = r'package s+([ w .]+);'
  import_pattern = r'import s+([ w .]+);'
  package_match = re.search(package_pattern, source_code_str)
  package_name = package_match.group(1) if package_match else None
  import_matches = re.findall(import_pattern, source_code_str)
  return package_name, import_matches

def extract_api_endpoints(source_code_str):
  endpoints = []
  # Enhanced patterns to catch more API endpoint variations
  mapping_patterns = {
    'RequestMapping': [
      r'@RequestMapping s* ( s*value s*= s*[" ']([^" ']+)[" ']',
      r'@RequestMapping s* ( s*[" ']([^" ']+)[" ']',
      r'@RequestMapping s* ( s*path s*= s*[" ']([^" ']+)[" ']'
    ],
    'GetMapping': [
      r'@GetMapping s* ( s*[" ']([^" ']+)[" ']',
      r'@GetMapping s* ( s*value s*= s*[" ']([^" ']+)[" ']',
      r'@GetMapping s* ( s*path s*= s*[" ']([^" ']+)[" ']'
    ],
    'PostMapping': [
      r'@PostMapping s* ( s*[" ']([^" ']+)[" ']',
      r'@PostMapping s* ( s*value s*= s*[" ']([^" ']+)[" ']',
      r'@PostMapping s* ( s*path s*= s*[" ']([^" ']+)[" ']'
    ],
    'PutMapping': [
      r'@PutMapping s* ( s*[" ']([^" ']+)[" ']',
      r'@PutMapping s* ( s*value s*= s*[" ']([^" ']+)[" ']'
    ],
    'DeleteMapping': [
      r'@DeleteMapping s* ( s*[" ']([^" ']+)[" ']',
      r'@DeleteMapping s* ( s*value s*= s*[" ']([^" ']+)[" ']'
    ],
    'PatchMapping': [
      r'@PatchMapping s* ( s*[" ']([^" ']+)[" ']',
      r'@PatchMapping s* ( s*value s*= s*[" ']([^" ']+)[" ']'
    ]
  }
  
  for mapping_type, patterns in mapping_patterns.items():
    for pattern in patterns:
      matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
      for match in matches:
        if match.strip(): # Only add non-empty matches
          endpoints.append({
            'type': mapping_type,
            'path': match.strip(),
            'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
          })
  
  # Also look for @RestController and @Controller classes
  controller_pattern = r'@(RestController|Controller)'
  if re.search(controller_pattern, source_code_str):
    # Extract class-level RequestMapping
    class_mapping = re.search(r'@RequestMapping s* ( s*[" ']([^" ']+)[" ']', source_code_str)
    base_path = class_mapping.group(1) if class_mapping else ""
    
    # Find methods with mappings
    method_pattern = r'@(GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping|RequestMapping) s*(?: ([^)]* ))? s*  s*(?:public|private|protected)? s* w+ s+( w+) s* ('
    method_matches = re.findall(method_pattern, source_code_str, re.MULTILINE)
    
    for mapping_type, method_name in method_matches:
      if base_path:
        full_path = f"{base_path.rstrip('/')}/{method_name}"
      else:
        full_path = f"/{method_name}"
      
      endpoints.append({
        'type': mapping_type,
        'path': full_path,
        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET',
        'method_name': method_name
      })
  
  return endpoints

def extract_database_entities(source_code_str):
  entities = []
  
  # Enhanced Entity detection
  entity_patterns = [
    r'@Entity s*(?: ([^)]* ))?',
    r'@Table s* ( s*name s*= s*[" ']([^" ']+)[" ']',
    r'@Entity s*  s*@Table s* ( s*name s*= s*[" ']([^" ']+)[" ']'
  ]
  
  for pattern in entity_patterns:
    if re.search(pattern, source_code_str, re.MULTILINE):
      # Extract table name from @Table annotation
      table_matches = re.findall(r'@Table s* ( s*name s*= s*[" ']([^" ']+)[" ']', source_code_str)
      for table_name in table_matches:
        if table_name.strip():
          entities.append({
            'type': 'table',
            'name': table_name.strip()
          })
      
      # If no @Table, try to infer from class name
      if not table_matches:
        class_match = re.search(r'public s+class s+( w+)', source_code_str)
        if class_match:
          class_name = class_match.group(1)
          # Convert CamelCase to snake_case for table name
          table_name = re.sub('([a-z0-9])([A-Z])', r' 1_ 2', class_name).lower()
          entities.append({
            'type': 'table',
            'name': table_name
          })
  
  # Enhanced Query detection
  query_patterns = [
    r'@Query s* ( s*[" ']([^" ']*(?:FROM|from) s+([ w]+)[^" ']*)[" ']',
    r'@Query s* ( s*value s*= s*[" ']([^" ']*(?:FROM|from) s+([ w]+)[^" ']*)[" ']',
    r'@Query s* ( s*nativeQuery s*= s*true s*, s*value s*= s*[" ']([^" ']*(?:FROM|from) s+([ w]+)[^" ']*)[" ']'
  ]
  
  for pattern in query_patterns:
    query_matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE)
    for match in query_matches:
      if isinstance(match, tuple) and len(match) >= 2:
        table_name = match[1].strip()
        if table_name and table_name.lower() not in ['select', 'where', 'order', 'group']:
          entities.append({
            'type': 'table',
            'name': table_name
          })
  
  # Repository pattern detection
  repository_pattern = r'interface s+( w+) s+extends s+.*Repository'
  repo_matches = re.findall(repository_pattern, source_code_str)
  for repo_name in repo_matches:
    # Extract entity name from repository name (e.g., UserRepository -> User)
    entity_name = repo_name.replace('Repository', '')
    if entity_name:
      entities.append({
        'type': 'table',
        'name': entity_name.lower()
      })
  
  return entities



def extract_interface_extends(source_code_str):
  extends_relationships = []
  
  # Interface extends
  interface_extends_pattern = r'interface s+( w+) s+extends s+([ w<>, s]+)'
  matches = re.findall(interface_extends_pattern, source_code_str)
  for interface_name, extends_clause in matches:
    parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]
    for parent in parent_interfaces:
      if parent:
        extends_relationships.append({
          'child_interface': interface_name,
          'parent_interface': parent,
          'full_extends': extends_clause.strip(),
          'type': 'interface_extends'
        })
  
  # Class extends
  class_extends_pattern = r'class s+( w+) s+extends s+([ w<>]+)'
  class_matches = re.findall(class_extends_pattern, source_code_str)
  for child_class, parent_class in class_matches:
    # Clean generic types
    parent_class = re.sub(r'<.*?>', '', parent_class).strip()
    if parent_class:
      extends_relationships.append({
        'child_interface': child_class,
        'parent_interface': parent_class,
        'full_extends': parent_class,
        'type': 'class_extends'
      })
  
  # Class implements
  implements_pattern = r'class s+( w+)(?: s+extends s+ w+)? s+implements s+([ w<>, s]+)'
  impl_matches = re.findall(implements_pattern, source_code_str)
  for class_name, implements_clause in impl_matches:
    interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
    for interface in interfaces:
      if interface:
        extends_relationships.append({
          'child_interface': class_name,
          'parent_interface': interface,
          'full_extends': implements_clause.strip(),
          'type': 'class_implements'
        })
  
  return extends_relationships



def build_enhanced_class_registry():
  class_registry = {}
  for root, _, files in os.walk(BASE_PATH):
    for file in files:
      if file.endswith('.java'):
        file_path = os.path.join(root, file)
        try:
          with open(file_path, 'r', encoding='utf-8') as f:
            source_code_str = f.read()
          
          package_name, imports = extract_package_and_imports(source_code_str)
          endpoints = extract_api_endpoints(source_code_str)
          db_entities = extract_database_entities(source_code_str)
          interface_extends = extract_interface_extends(source_code_str)
          
          class_name = file.replace('.java', '')
          fqcn = f'{package_name}.{class_name}' if package_name else class_name
          
          class_registry[class_name] = {
            'fqcn': fqcn,
            'package': package_name,
            'file_path': file_path,
            'imports': imports,
            'endpoints': endpoints,
            'db_entities': db_entities,
            'interface_extends': interface_extends
          }
          
          # Debug output for endpoints and DB entities
          if endpoints:
            print(f"🔗 Found {len(endpoints)} endpoints in {class_name}")
          if db_entities:
            print(f"🗄️ Found {len(db_entities)} DB entities in {class_name}")
            
        except Exception as e:
          print(f" Error processing {file}: {e}")
          continue
  
  return class_registry

class_registry = build_enhanced_class_registry()
print(f' Class registry built with {len(class_registry)} classes')

# ========== STAGE 2: AST EXTRACTION ==========

def extract_ast_structure(file_path):
  records = []
  source_code = read_source_code(file_path)
  tree = parser.parse(source_code)
  root_node = tree.root_node
  file_name = os.path.basename(file_path)

  def clean_node_name(name):
    """Clean node names to remove prefixes and suffixes"""
    if not name:
      return name
    
    # Remove common prefixes
    prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
    for prefix in prefixes_to_remove:
      if name.lower().startswith(prefix):
        name = name[len(prefix):]
    
    # Remove file extensions
    name = re.sub(r' .(java|class)$', '', name, flags=re.IGNORECASE)
    
    return name.strip()

  def traverse(node, parent_type=None, parent_name=None):
    # Handle class declarations
    if node.type == 'class_declaration':
      class_name = None
      for child in node.children:
        if child.type == 'identifier':
          class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
          
          # File -> Class relationship
          records.append({
            'source_node': file_name,
            'source_type': 'file',
            'destination_node': class_name,
            'destination_type': 'class',
            'relationship': 'declares',
            'file_path': file_path
          })
          
          # Add API endpoints from registry
          class_info = class_registry.get(class_name, {})
          endpoints = class_info.get('endpoints', [])
          for ep in endpoints:
            endpoint_name = f"{ep['method']} {ep['path']}"
            records.append({
              'source_node': class_name,
              'source_type': 'class',
              'destination_node': endpoint_name,
              'destination_type': 'endpoint',
              'relationship': 'declares',
              'file_path': file_path
            })
          
          # Add database entities from registry
          db_entities = class_info.get('db_entities', [])
          for entity in db_entities:
            records.append({
              'source_node': class_name,
              'source_type': 'class',
              'destination_node': entity['name'],
              'destination_type': 'table',
              'relationship': 'maps_to',
              'file_path': file_path
            })
          
          # Add extends/implements relationships from registry
          interface_extends = class_info.get('interface_extends', [])
          for ext_rel in interface_extends:
            rel_type = 'extends' if ext_rel.get('type') == 'class_extends' else 'implements'
            records.append({
              'source_node': class_name,
              'source_type': 'class',
              'destination_node': ext_rel['parent_interface'],
              'destination_type': 'interface' if rel_type == 'implements' else 'class',
              'relationship': rel_type,
              'file_path': file_path
            })
          break
      
      # Traverse children with class context
      for child in node.children:
        traverse(child, 'class', class_name)
        
    # Handle interface declarations
    elif node.type == 'interface_declaration':
      interface_name = None
      for child in node.children:
        if child.type == 'identifier':
          interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
          records.append({
            'source_node': file_name,
            'source_type': 'file',
            'destination_node': interface_name,
            'destination_type': 'interface',
            'relationship': 'declares',
            'file_path': file_path
          })
          break
      
      # Traverse children with interface context
      for child in node.children:
        traverse(child, 'interface', interface_name)
        
    # Handle method declarations - FIXED HIERARCHY
    elif node.type == 'method_declaration':
      method_name = None
      for child in node.children:
        if child.type == 'identifier':
          method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
          
          # CORRECT: Class -> Method (not Method -> Class)
          if parent_name and parent_type in ['class', 'interface']:
                 records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': file_path
                        })
                    break
            
            # Traverse children with method context
            for child in node.children:
                traverse(child, 'method', method_name)
                
        # Handle field declarations - FIXED HIERARCHY
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            
                            # CORRECT: Class -> Variable (not Variable -> Class)
                            if parent_name and parent_type == 'class':
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': parent_type,
                                    'destination_node': field_name,
                                    'destination_type': 'variable',
                                    'relationship': 'has_field',
                                    'file_path': file_path
                                })
                                
        # Handle variable usage in methods - FIXED HIERARCHY
        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and var_name != 'this' and parent_name:
                        # CORRECT: Method -> Variable (not Variable -> Method)
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': var_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })
                        
        # Handle field access in methods
        elif node.type == 'field_access' and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    field_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if field_name and field_name != 'this' and parent_name:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': field_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })
        
        # Handle return statements
        elif node.type == 'return_statement' and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and parent_name:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': var_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })
        else:
            # Continue traversing for other node types
            for child in node.children:
                traverse(child, parent_type, parent_name)

    traverse(root_node)
    return records

ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f' Error processing {file}: {e}')
                continue

df_ast = pd.DataFrame(ast_records)
print(f'Stage 2 Complete: {len(df_ast)} AST relationships extracted')

# ========== STAGE 3: LLM EXTRACTION WITH AST CONTEXT ==========


def build_enhanced_system_prompt(file_path, ast_df, class_registry):
  ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()
  ast_context = ''
  for _, row in ast_subset.iterrows():
    ast_context += f"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']} "
  
  registry_context = 'Known Classes: '
  for class_name, info in class_registry.items():
    registry_context += f'- {class_name} (FQCN: {info["fqcn"]}) '
    if len(info.get('endpoints', [])) > 0:
      registry_context += f' * {len(info["endpoints"])} API endpoint(s) '
    if len(info.get('db_entities', [])) > 0:
      registry_context += f' * {len(info["db_entities"])} DB entity/entities '
  
  prompt = f"""
You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:

CONTEXT:
{registry_context}

AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):
{ast_context}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
  - file -[declares]-> class
  - class -[declares]-> method 
  - class -[has_field]-> variable
  - method -[uses]-> variable
  - class -[declares]-> endpoint
  - class -[maps_to]-> table
3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)
4. Extract database tables from @Entity, @Table, @Query annotations
5. Extract interface extends and class implements relationships
6. NEVER create reverse relationships (method->class, variable->method, etc.)
7. Follow the AST RELATIONSHIPS above for correct structure
8. Clean node names (remove "method:", "class:" prefixes)

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
  return prompt


splitter = RecursiveCharacterTextSplitter.from_language(
  language=LC_Language.JAVA,
  chunk_size=4000,
  chunk_overlap=200
)

java_docs, split_docs = [], []
for root, _, files in os.walk(BASE_PATH):
  for file in files:
    if file.endswith('.java'):
      try:
        loader = TextLoader(os.path.join(root, file))
        java_docs.extend(loader.load())
      except Exception as e:
        print(f" Error loading {file}: {e}")
        continue

for doc in java_docs:
  split_docs.extend(splitter.split_documents([doc]))

all_llm_lineage = []
for chunk in tqdm(split_docs, desc='Stage 3: Enhanced LLM Extraction'):
  file_path = chunk.metadata.get('source')
  system_prompt = build_enhanced_system_prompt(file_path, df_ast, class_registry)
  
  transformer = LLMGraphTransformer(
    llm=llm,
    additional_instructions=system_prompt,
    allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint'],
    allowed_relationships=[
      ('file', 'declares', 'class'),
      ('file', 'declares', 'interface'),
      ('class', 'declares', 'method'),
      ('interface', 'declares', 'method'),
      ('class', 'declares', 'endpoint'),
      ('method', 'calls', 'method'),
      ('class', 'has_field', 'variable'),
      ('method', 'uses', 'variable'),
      ('class', 'uses', 'class'),
      ('interface', 'extends', 'interface'),
      ('class', 'extends', 'class'),
      ('class', 'implements', 'interface'),
      ('class', 'maps_to', 'table'),
      ('method', 'reads_from', 'table'),
      ('method', 'writes_to', 'table'),
    ],
    strict_mode=True,
    node_properties=False,
    relationship_properties=False,
  )
  
  try:
    graph_docs = transformer.convert_to_graph_documents([chunk])
    for gd in graph_docs:
      for rel in gd.relationships:
        s_node = rel.source.id.strip()
        s_type = rel.source.type.strip().lower()
        t_node = rel.target.id.strip()
        t_type = rel.target.type.strip().lower()
        rel_type = rel.type.strip().lower()

        def normalize_entity(entity_name, entity_type):
          if not entity_name:
            return entity_name
          
          # Remove prefixes
          prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
          for prefix in prefixes:
            if entity_name.lower().startswith(prefix):
              entity_name = entity_name[len(prefix):]
          
          # Remove file extensions
          entity_name = re.sub(r' .(java|class)$', '', entity_name, flags=re.IGNORECASE)
          
          # Clean dots for class names
          if entity_type in ['class', 'method'] and '.' in entity_name:
            entity_name = entity_name.split('.')[-1]
          
          # Match with class registry for consistency
          if entity_type == 'class':
            for class_name in class_registry.keys():
              if entity_name.lower() == class_name.lower():
                return class_name.lower()
          
          return entity_name.lower()

        s_node = normalize_entity(s_node, s_type)
        t_node = normalize_entity(t_node, t_type)

        # Skip invalid relationships
        if s_node == t_node and s_type == t_type:
          continue
        if not s_node or not t_node:
          continue
        
        # Enforce correct relationship directions
        valid_directions = {
          ('file', 'declares', 'class'),
          ('file', 'declares', 'interface'),
          ('class', 'declares', 'method'),
          ('interface', 'declares', 'method'),
          ('class', 'declares', 'endpoint'),
          ('class', 'has_field', 'variable'),
          ('method', 'uses', 'variable'),
          ('class', 'maps_to', 'table'),
          ('class', 'extends', 'class'),
          ('class', 'implements', 'interface'),
          ('interface', 'extends', 'interface'),
          ('method', 'calls', 'method'),
          ('method', 'reads_from', 'table'),
          ('method', 'writes_to', 'table')
        }
        
        if (s_type, rel_type, t_type) not in valid_directions:
          continue

        all_llm_lineage.append({
          'source_node': s_node,
          'source_type': s_type,
          'destination_node': t_node,
          'destination_type': t_type,
          'relationship': rel_type,
          'file_path': file_path
        })
  except Exception as e:
    continue

df_llm_lineage = pd.DataFrame(all_llm_lineage)
print(f' Stage 3 Complete: {len(df_llm_lineage)} LLM relationships extracted')


# ========== STAGE 4: DATA NORMALIZATION AND CLEANING ==========

# Clean node names function
def clean_dataframe_names(df):
  """Clean node names in dataframe"""
  for col in ["source_node", "destination_node"]:
    if col in df.columns:
      df[col] = df[col].astype(str).apply(lambda x: re.sub(r'^(method|class|variable|field|table|endpoint):', '', x, flags=re.IGNORECASE))
      df[col] = df[col].apply(lambda x: re.sub(r' .(java|class)$', '', x, flags=re.IGNORECASE))
      df[col] = df[col].str.strip()
  return df

# Clean all dataframes
df_folders = clean_dataframe_names(df_folders)
df_files = clean_dataframe_names(df_files)
df_llm_lineage = clean_dataframe_names(df_llm_lineage)

# Normalize dataframes (excluding AST to reduce noise)
for df in [df_folders, df_files, df_llm_lineage]:
  for col in ["source_node", "destination_node", "source_type", "destination_type", "relationship"]:
    if col in df.columns:
      df[col] = df[col].astype(str).str.strip().str.lower()

# Combine dataframes (EXCLUDING df_ast to reduce noise)
df_combined = pd.concat([df_folders, df_files, df_llm_lineage], ignore_index=True)
df_combined.drop_duplicates(
  subset=["source_node", "source_type", "destination_node", "destination_type", "relationship"],
  inplace=True
)

# Remove self-references
df_combined = df_combined[
  ~((df_combined['source_node'] == df_combined['destination_node']) &
   (df_combined['source_type'] == df_combined['destination_type']))
]

# Remove empty/null entries
df_combined = df_combined[
  (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &
  (df_combined['source_node'] != '') & (df_combined['destination_node'] != '') &
  (df_combined['source_node'] != 'none') & (df_combined['destination_node'] != 'none')
]

print(f' Combined data: {len(df_combined)} relationships after deduplication and cleaning')


# ========== STAGE 5: FILE VS CLASS CORRECTION ==========


# Create a corrected dataframe for insertion
df_corrected = df_combined.copy()

# Fix File and Class naming for consistency
for idx, row in df_corrected.iterrows():
  source_node = str(row["source_node"]).strip()
  dest_node = str(row["destination_node"]).strip()
  source_type = str(row["source_type"]).strip().lower()
  dest_type = str(row["destination_type"]).strip().lower()

  # --- File Node Correction ---
  if source_type == "file":
    match = df_files[df_files["destination_node"].str.replace(".java", "", case=False) == source_node.replace(".java", "")]
    if not match.empty:
      df_corrected.at[idx, "source_node"] = match.iloc[0]["destination_node"]

  if dest_type == "file":
    match = df_files[df_files["destination_node"].str.replace(".java", "", case=False) == dest_node.replace(".java", "")]
    if not match.empty:
      df_corrected.at[idx, "destination_node"] = match.iloc[0]["destination_node"]

  # --- Class Node Correction ---
  if source_type == "class":
    clean_name = source_node.replace(".java", "")
    for class_name in class_registry.keys():
      if class_name.lower() == clean_name.lower():
        df_corrected.at[idx, "source_node"] = class_name.lower()
        break

  if dest_type == "class":
    clean_name = dest_node.replace(".java", "")
    for class_name in class_registry.keys():
      if class_name.lower() == clean_name.lower():
        df_corrected.at[idx, "destination_node"] = class_name.lower()
        break
      
print(f' Stage 5 Complete: {len(df_corrected)} corrected relationships ready for Neo4j')

# ========== STAGE 6: NEO4J INSERTION ==========

# Clear existing data
graph.query("MATCH (n) DETACH DELETE n")

# Insert clean corrected data
for idx, row in tqdm(df_corrected.iterrows(), total=len(df_corrected), desc="Final Neo4j Insertion"):
  try:
    source_node = str(row["source_node"]).strip()
    dest_node = str(row["destination_node"]).strip()
    source_type = str(row["source_type"]).strip().capitalize()
    dest_type = str(row["destination_type"]).strip().capitalize()
    relationship = str(row["relationship"]).strip().upper()

    if not all([source_node, dest_node, source_type, dest_type, relationship]):
      continue

    query = f"""
    MERGE (s:{source_type} {{name: $source_node}})
    MERGE (t:{dest_type} {{name: $destination_node}})
    MERGE (s)-[:{relationship}]->(t)
    """
    graph.query(query, {
      "source_node": source_node,
      "destination_node": dest_node
    })
  except Exception as e:
    pass # Optionally collect failed rows for review

print(" Final consistent data pushed to Neo4j.")


# ========== VALIDATION ==========
def validate_graph_statistics():
  node_count = graph.query("MATCH (n) RETURN count(n) as count")[0]["count"]
  rel_count = graph.query("MATCH ()-[r]->() RETURN count(r) as count")[0]["count"]

  print(f"Total Nodes in Neo4j: {node_count}")
  print(f"Total Relationships in Neo4j: {rel_count}")
  
  # Show breakdown by node type
  node_types = graph.query("MATCH (n) RETURN labels(n)[0] as type, count(n) as count ORDER BY count DESC")
  print(' Node Types:')
  for row in node_types:
    print(f' {row["type"]}: {row["count"]}')
  
  # Show breakdown by relationship type
  rel_types = graph.query("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY count DESC")
  print(' Relationship Types:')
  for row in rel_types:
    print(f' {row["type"]}: {row["count"]}')
  
  # Show sample endpoints and tables
  endpoints = graph.query("MATCH (n:Endpoint) RETURN n.name as name LIMIT 5")
  if endpoints:
    print(' Sample API Endpoints:')
    for ep in endpoints:
      print(f' {ep["name"]}')
  
  tables = graph.query("MATCH (n:Table) RETURN n.name as name LIMIT 5")
  if tables:
    print(' Sample Database Tables:')
    for table in tables:
      print(f' {table["name"]}')

validate_graph_statistics()
