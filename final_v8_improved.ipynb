# ========== STAGE 2B: IMPROVED FILE-TO-CLASS RELATIONSHIPS ==========

def extract_file_class_relationships():
    """Extract file-to-class relationships with improved name cleaning"""
    relationships = []
    class_registry = {}
    
    # Get all Java files from Stage 2
    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']
    
    for _, file_row in java_files.iterrows():
        file_path = file_row.get('file_path')
        file_name = file_row['destination_node']
        
        if not file_path or not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Extract class names using regex
            class_pattern = r'(?:public\s+)?(?:class|interface)\s+(\w+)'
            class_matches = re.findall(class_pattern, source_code)
            
            for class_name in class_matches:
                # Apply improved name cleaning
                clean_class_name = extract_clean_name(class_name, 'class')
                
                # File declares Class
                relationships.append({
                    'source_node': file_name,
                    'source_type': 'File',
                    'destination_node': clean_class_name,
                    'destination_type': 'Class',
                    'relationship': 'DECLARES',
                    'file_path': file_path
                })
                
                # Update class registry with file information
                if clean_class_name not in class_registry:
                    class_registry[clean_class_name] = {}
                
                class_registry[clean_class_name].update({
                    'file_path': file_path,
                    'file_name': file_name,
                    'source_code': source_code
                })
                
        except Exception as e:
            print(f"⚠️ Error processing file {file_name}: {e}")
            continue
    
    return relationships, class_registry

# Execute Stage 2B
file_class_relationships, class_registry = extract_file_class_relationships()
df_file_class = pd.DataFrame(file_class_relationships)

# Store Stage 2B results in memory
memory['stage_2b_results'] = {
    'relationships': len(df_file_class),
    'classes_found': len([r for r in file_class_relationships if r['destination_type'] == 'Class'])
}

# Add validated edges
for _, row in df_file_class.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)

save_memory(memory)

print(f"✅ Stage 2B Complete: {len(df_file_class)} file-to-class relationships extracted")
print(f"🏛️ Classes found: {memory['stage_2b_results']['classes_found']}")
print(f"📚 Class registry updated with {len(class_registry)} classes")