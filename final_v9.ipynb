{"cells": [{"cell_type": "code", "execution_count": 195, "id": "stage1_configuration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 1 Complete: Configuration loaded and Neo4j cleared\n"]}], "source": ["# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========\n", "import os\n", "import json\n", "import re\n", "import uuid\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "import threading\n", "\n", "# Tree-sitter for AST parsing\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "# LangChain components\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain.schema import Document\n", "\n", "# Configuration\n", "BASE_PATH = Path(r\"C:/Shaik/sample/LineageTestProject\")\n", "\n", "# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"test\"\n", "\n", "# Initialize connections\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# Azure OpenAI Configuration\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "# Temp variables to filter out\n", "TEMP_VARIABLES = {\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    'temp', 'tmp', 'temporary', 'temp1', 'temp2',\n", "    'count', 'counter', 'index', 'idx', 'iter',\n", "    'result', 'res', 'ret', 'val', 'value',\n", "    'item', 'elem', 'element', 'obj', 'object',\n", "    'str', 'string', 'num', 'number', 'flag',\n", "    'bool', 'boolean', 'arr', 'array', 'list',\n", "    'map', 'set', 'data', 'info', 'param', 'arg','Status'\n", "}\n", "\n", "# Memory storage\n", "MEMORY_FILE = \"lineage_memory_v9.json\"\n", "memory_lock = threading.Lock()\n", "\n", "def load_memory():\n", "    try:\n", "        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "            def convert_from_json(obj):\n", "                if isinstance(obj, dict):\n", "                    result = {}\n", "                    for k, v in obj.items():\n", "                        if k == 'validated_edges' and isinstance(v, list):\n", "                            result[k] = set(v)\n", "                        else:\n", "                            result[k] = convert_from_json(v)\n", "                    return result\n", "                elif isinstance(obj, list):\n", "                    return [convert_from_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            return convert_from_json(data)\n", "    except FileNotFoundError:\n", "        return {\n", "            'class_registry': {},\n", "            'validated_edges': set(),\n", "            'variable_contexts': {},\n", "            'method_signatures': {}\n", "        }\n", "\n", "def save_memory(memory):\n", "    with memory_lock:\n", "        try:\n", "            memory_copy = memory.copy()\n", "            def convert_for_json(obj):\n", "                if isinstance(obj, set):\n", "                    return list(obj)\n", "                elif isinstance(obj, dict):\n", "                    return {k: convert_for_json(v) for k, v in obj.items()}\n", "                elif isinstance(obj, list):\n", "                    return [convert_for_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            memory_copy = convert_for_json(memory_copy)\n", "            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:\n", "                json.dump(memory_copy, f, indent=2, ensure_ascii=False)\n", "        except Exception as e:\n", "            print(f\"Memory save error: {e}\")\n", "\n", "# Initialize memory\n", "memory = load_memory()\n", "\n", "# Clear Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\"Stage 1 Complete: Configuration loaded and Neo4j cleared\")"]}, {"cell_type": "code", "execution_count": 196, "id": "utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Utility functions loaded - ALL Java files will be processed (no file filtering)\n"]}], "source": ["# ========== UTILITY FUNCTIONS ==========\n", "\n", "def to_pascal_case(text):\n", "    if not text:\n", "        return text\n", "    \n", "    # Remove file extensions first\n", "    text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # Handle file paths - extract just the filename\n", "    if '/' in text or '\\\\' in text:\n", "        text = os.path.basename(text)\n", "        text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # IMPROVED: Don't assume text is already PascalCase - always normalize it\n", "    # This fixes issues like 'Userservice' vs 'UserService'\n", "    \n", "    # Handle camelCase -> PascalCase (e.g., userService -> UserService)\n", "    # Split on capital letters to handle camelCase properly\n", "    if not re.search(r'[_\\-\\s]', text):  # No delimiters, might be camelCase\n", "        # Split camelCase: userService -> ['user', 'Service']\n", "        camel_parts = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\\b)', text)\n", "        if camel_parts and len(camel_parts) > 1:\n", "            parts = camel_parts\n", "        else:\n", "            # Single word - ensure proper capitalization\n", "            return text[0].upper() + text[1:].lower() if len(text) > 1 else text.upper()\n", "    else:\n", "        # Split on common delimiters\n", "        parts = re.split(r'[_\\-\\s]+', text)\n", "    \n", "    # Capitalize each part\n", "    result = ''\n", "    for part in parts:\n", "        if part:\n", "            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()\n", "    \n", "    return result if result else text\n", "\n", "def extract_clean_name(full_name, name_type):\n", "    if not full_name:\n", "        return full_name\n", "    \n", "    # Remove common prefixes\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "    for prefix in prefixes:\n", "        if full_name.lower().startswith(prefix):\n", "            full_name = full_name[len(prefix):]\n", "    \n", "    # Remove file extensions\n", "    full_name = re.sub(r'\\.(java|class)$', '', full_name, flags=re.IGNORECASE)\n", "    \n", "    # Handle file.class patterns - extract only class name\n", "    if '.' in full_name and name_type.lower() in ['class', 'interface']:\n", "        parts = full_name.split('.')\n", "        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Handle classname.method patterns - extract only method name\n", "    if '.' in full_name and name_type.lower() == 'method':\n", "        parts = full_name.split('.')\n", "        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Apply PascalCase for classes, methods, files, folders\n", "    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:\n", "        return to_pascal_case(full_name)\n", "    \n", "    # For variables, keep original name (context handled separately)\n", "    if name_type.lower() == 'variable':\n", "        if '.' in full_name:\n", "            return full_name.split('.')[-1]  # Return only variable name\n", "        return full_name\n", "    \n", "    # For tables, apply PascalCase\n", "    if name_type.lower() == 'table':\n", "        return to_pascal_case(full_name)\n", "    \n", "    return full_name\n", "\n", "def is_temp_variable(var_name):\n", "    if not var_name:\n", "        return True\n", "    var_lower = var_name.lower().strip()\n", "    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1\n", "\n", "def escape_braces_for_langchain(text):\n", "    if not text:\n", "        return text\n", "    return text.replace('{', '{{').replace('}', '}}')\n", "\n", "print(\"✅ Utility functions loaded - ALL Java files will be processed (no file filtering)\")"]}, {"cell_type": "code", "execution_count": 197, "id": "stage2_folder_file_hierarchy", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 2 Complete: 10 folder/file relationships extracted\n", "Folders: 2, Files: 4\n", "File-Class relationships: 4\n"]}], "source": ["# ========== STAGE 2: FOLDER-FILE HIERARCHY ==========\n", "\n", "def extract_folder_file_hierarchy():\n", "    relationships = []\n", "    base_folder = to_pascal_case(BASE_PATH.name)\n", "\n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        current_path = Path(root)\n", "        rel_path = current_path.relative_to(BASE_PATH)\n", "\n", "        # Determine current folder name and its parent\n", "        if rel_path != Path('.'):\n", "            folder_name = to_pascal_case(current_path.name)\n", "            parent_rel_path = current_path.parent.relative_to(BASE_PATH)\n", "            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)\n", "\n", "            relationships.append({\n", "                'source_node': parent_name,\n", "                'source_type': 'Folder',\n", "                'destination_node': folder_name,\n", "                'destination_type': 'Folder',\n", "                'relationship': 'CONTAINS'\n", "            })\n", "            current_folder_name = folder_name\n", "        else:\n", "            current_folder_name = base_folder\n", "\n", "        # Process files inside the folder\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_name = extract_clean_name(file, 'file')\n", "                relationships.append({\n", "                    'source_node': current_folder_name,\n", "                    'source_type': 'Folder',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'File',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': str(current_path / file)\n", "                })\n", "\n", "    return relationships\n", "\n", "def extract_file_class_relationships_ast(hierarchy_df):\n", "    file_class_relationships = []\n", "    \n", "    # Get all Java files from the hierarchy data\n", "    java_files = hierarchy_df[hierarchy_df['destination_type'] == 'File']\n", "    \n", "    for _, file_row in java_files.iterrows():\n", "        if 'file_path' in file_row and file_row['file_path']:\n", "            file_path = file_row['file_path']\n", "            file_name = file_row['destination_node']\n", "            \n", "            try:\n", "                # Read and parse the Java file\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    source_code_str = f.read()\n", "                \n", "                # Process ALL Java files - no filtering to ensure complete pipeline coverage\n", "                \n", "                source_code = source_code_str.encode('utf-8')\n", "                tree = parser.parse(source_code)\n", "                root_node = tree.root_node\n", "                \n", "                # Find class declarations\n", "                def find_classes(node):\n", "                    classes = []\n", "                    if node.type == 'class_declaration':\n", "                        # Find class name\n", "                        for child in node.children:\n", "                            if child.type == 'identifier':\n", "                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                                classes.append(to_pascal_case(class_name))\n", "                                break\n", "                    \n", "                    # Recursively search child nodes\n", "                    for child in node.children:\n", "                        classes.extend(find_classes(child))\n", "                    \n", "                    return classes\n", "                \n", "                classes_in_file = find_classes(root_node)\n", "                \n", "                # Create file-class relationships\n", "                for class_name in classes_in_file:\n", "                    file_class_relationships.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'File',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'Class',\n", "                        'relationship': 'DECLARES',\n", "                        'file_path': file_path\n", "                    })\n", "                    \n", "            except Exception as e:\n", "                print(f\"Error processing {file_path}: {e}\")\n", "                continue\n", "    \n", "    return file_class_relationships\n", "\n", "# Execute Stage 2\n", "folder_file_relationships = extract_folder_file_hierarchy()\n", "df_hierarchy = pd.DataFrame(folder_file_relationships)\n", "\n", "# Execute Stage 2B: Extract file-class relationships\n", "file_class_relationships = extract_file_class_relationships_ast(df_hierarchy)\n", "df_file_class = pd.DataFrame(file_class_relationships)\n", "\n", "# Append file-class relationships to the hierarchy DataFrame\n", "if len(df_file_class) > 0:\n", "    df_hierarchy = pd.concat([df_hierarchy, df_file_class], ignore_index=True)\n", "\n", "# Store in memory\n", "for _, row in df_hierarchy.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "save_memory(memory)\n", "\n", "print(f\"Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted\")\n", "print(f\"Folders: {len([r for r in folder_file_relationships if r['destination_type'] == 'Folder'])}, Files: {len([r for r in folder_file_relationships if r['destination_type'] == 'File'])}\")\n", "print(f\"File-Class relationships: {len(df_file_class)}\")"]}, {"cell_type": "code", "execution_count": 198, "id": "stage3_class_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 3 Complete: Class registry built with 4 classes\n", "Method signatures stored: 3\n"]}], "source": ["# ========== STAGE 3: LIGHTWEIGHT AST EXTRACTION ==========\n", "\n", "# Patterns for analysis\n", "PACKAGE_PATTERN = r'package\\s+([\\w\\.]+);'\n", "IMPORT_PATTERN = r'import\\s+([\\w\\.]+);'\n", "MAPPING_PATTERNS = {\n", "    'GetMapping': r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PostMapping': r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PutMapping': r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'DeleteMapping': r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'RequestMapping': r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "}\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    package_match = re.search(PACKAGE_PATTERN, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(IMPORT_PATTERN, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    endpoints = []\n", "    for mapping_type, pattern in MAPPING_PATTERNS.items():\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            path = match.strip()\n", "            if path:\n", "                method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                endpoints.append({\n", "                    'type': mapping_type,\n", "                    'path': path,\n", "                    'method': method\n", "                })\n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    entities = []\n", "\n", "    # @Entity/@Table extraction\n", "    if \"@Entity\" in source_code_str:\n", "        table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "        for table_name in table_matches:\n", "            entities.append({'type': 'table', 'name': table_name.strip()})\n", "\n", "        if not table_matches:\n", "            class_match = re.search(r'(public\\s+)?(class|abstract class|interface)\\s+(\\w+)', source_code_str)\n", "            if class_match:\n", "                class_name = class_match.group(3)\n", "                snake_case = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                entities.append({'type': 'table', 'name': snake_case})\n", "\n", "    # @Query: detect raw SQL or JPQL references to tables\n", "    query_pattern = r'@Query\\s*\\([^)]*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']'\n", "    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "    for _, table in query_matches:\n", "        table = table.strip()\n", "        if table and table.lower() not in {'select', 'where', 'group', 'order'}:\n", "            entities.append({'type': 'table', 'name': table})\n", "\n", "    return entities\n", "\n", "def build_class_registry():\n", "    class_registry = {}\n", "    \n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    # Process ALL Java files - no filtering to ensure complete pipeline coverage\n", "                    \n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    \n", "                    # Apply improved name cleaning\n", "                    class_name = extract_clean_name(file.replace('.java', ''), 'class')\n", "                    fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "                    \n", "                    class_registry[class_name] = {\n", "                        'fqcn': fqcn,\n", "                        'package': package_name,\n", "                        'file_path': file_path,\n", "                        'imports': imports,\n", "                        'endpoints': endpoints,\n", "                        'db_entities': db_entities,\n", "                        'source_code': source_code_str\n", "                    }\n", "                    \n", "                    # Store method signatures in memory for cross-stage reference\n", "                    methods = re.findall(r'(?:public|private|protected)\\s+\\w+\\s+(\\w+)\\s*\\(', source_code_str)\n", "                    clean_methods = [extract_clean_name(m, 'method') for m in methods]\n", "                    \n", "                    for method in clean_methods:\n", "                        memory['method_signatures'][method] = {\n", "                            'class': class_name,\n", "                            'file_path': file_path\n", "                        }\n", "                    \n", "                except Exception as e:\n", "                    print(f\"Error processing {file}: {e}\")\n", "                    continue\n", "    \n", "    return class_registry\n", "\n", "def extract_ast_context_only(file_path):\n", "    \"\"\"Extract AST structure for context only (not for final output)\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            source_code = f.read().encode('utf-8')\n", "        \n", "        tree = parser.parse(source_code)\n", "        root_node = tree.root_node\n", "        \n", "        context_info = []\n", "        \n", "        def traverse_for_context(node, parent_type=None, parent_name=None):\n", "            if node.type == 'class_declaration':\n", "                for child in node.children:\n", "                    if child.type == 'identifier':\n", "                        class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                        context_info.append(f\"class:{class_name}\")\n", "                        for grandchild in node.children:\n", "                            traverse_for_context(grandchild, 'class', class_name)\n", "                        break\n", "            elif node.type == 'method_declaration' and parent_type == 'class':\n", "                for child in node.children:\n", "                    if child.type == 'identifier':\n", "                        method_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                        context_info.append(f\"method:{parent_name}.{method_name}\")\n", "                        break\n", "            else:\n", "                for child in node.children:\n", "                    traverse_for_context(child, parent_type, parent_name)\n", "        \n", "        traverse_for_context(root_node)\n", "        return \"\\n\".join(context_info)\n", "        \n", "    except Exception as e:\n", "        return f\"AST parsing error: {e}\"\n", "\n", "# Execute AST extraction\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f'⚠️ Error processing {file}: {e}')\n", "                continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'✅ Stage 3 Complete: {len(df_ast)} AST relationships extracted')\n", "\n", "# Store AST results in memory\n", "memory['ast_relationships'] = len(df_ast)\n", "save_memory(memory)"]}, {"cell_type": "code", "execution_count": 199, "id": "stage4_llm_processing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total documents prepared: 4 (processed directly from files)\n", "Stage 4 Optimization: No class_registry dependency - truly lightweight!\n"]}], "source": ["# ========== STAGE 4: LLM PROCESSING WITH AST CONTEXT ==========\n", "\n", "def build_optimized_prompt(file_path, class_name):\n", "    # Build AST context for the specific file\n", "    ast_context = extract_ast_context_only(file_path)\n", "    \n", "    # Build simplified prompt for structural extraction only\n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine focused on STRUCTURAL relationships only.\n", "\n", "CURRENT FILE: {class_name}\n", "\n", "AST CONTEXT (for reference only):\n", "{ast_context}\n", "\n", "EXTRACTION RULES - STRUCTURAL ONLY:\n", "\n", "1. Extract ONLY basic structural relationships:\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable\n", "   - method -[uses]-> variable\n", "   - class -[declares]-> endpoint\n", "   - class -[maps_to]-> table\n", "\n", "2. Use SIMPLE names only (remove prefixes like method:, class:, etc.)\n", "3. NEVER create reverse relationships (method->class, variable->method, etc.)\n", "4. Clean node names (remove method:, class: prefixes)\n", "\n", "Extract relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the structural triples, no explanations.\n", "\"\"\"\n", "    return prompt.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "def smart_chunk_strategy(file_path, content):\n", "    lines = content.count('\\n') + 1\n", "    \n", "    # Escape curly braces to prevent <PERSON><PERSON><PERSON><PERSON> template conflicts\n", "    escaped_content = escape_braces_for_langchain(content)\n", "    \n", "    if lines <= 1000:\n", "        return [{'content': escaped_content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]\n", "    else:\n", "        splitter = RecursiveCharacterTextSplitter.from_language(\n", "            language=LC_Language.JAVA,\n", "            chunk_size=8000,\n", "            chunk_overlap=400\n", "        )\n", "        doc = Document(page_content=escaped_content, metadata={'source': file_path})\n", "        chunks = splitter.split_documents([doc])\n", "        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]\n", "\n", "# Collect documents with smart chunking - LIGHTWEIGHT (no class_registry dependency)\n", "smart_docs = []\n", "\n", "# Process files directly from filesystem\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                \n", "                # Process ALL Java files - no filtering to ensure complete pipeline coverage\n", "                \n", "                class_name = extract_clean_name(file.replace('.java', ''), 'class')\n", "                chunks = smart_chunk_strategy(file_path, content)\n", "                \n", "                for chunk in chunks:\n", "                    chunk['metadata']['class_name'] = class_name\n", "                smart_docs.extend(chunks)\n", "                \n", "            except Exception as e:\n", "                print(f\"Error reading {file_path}: {e}\")\n", "                continue\n", "\n", "print(f\"Total documents prepared: {len(smart_docs)} (processed directly from files)\")\n", "print(f\"Stage 4 Optimization: No class_registry dependency - truly lightweight!\")"]}, {"cell_type": "code", "execution_count": 200, "id": "stage4b_llm_extraction", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Stage 4B: Lightweight LLM Processing: 100%|██████████| 4/4 [00:16<00:00,  4.12s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Stage 4B Complete: 25 structural relationships extracted\n", "Memory usage: Lightweight - no heavy memory or registry used\n", "Variable contexts stored for Stage 5: 14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 4B: TRULY LIGHTWEIGHT LLM EXTRACTION (NO CLASS_REGISTRY/MEMORY) ==========\n", "\n", "all_llm_lineage = []\n", "\n", "# Process each chunk independently - no batching needed\n", "for doc_info in tqdm(smart_docs, desc='Stage 4B: Lightweight LLM Processing'):\n", "    file_path = doc_info['metadata'].get('source')\n", "    class_name = doc_info['metadata'].get('class_name', 'Unknown')\n", "    \n", "    # Only use AST context for this specific file - no heavy memory\n", "    ast_context = extract_ast_context_only(file_path)\n", "    \n", "    # Lightweight prompt - only structural relationships\n", "    enhanced_prompt = f\"\"\"\n", "Extract ONLY basic structural relationships from this Java code chunk.\n", "\n", "FILE: {class_name}\n", "AST CONTEXT: {ast_context}\n", "\n", "Extract only:\n", "- class -[declares]-> method\n", "- class -[has_field]-> variable  \n", "- method -[uses]-> variable\n", "- class -[declares]-> endpoint\n", "- class -[maps_to]-> table\n", "- method -[calls]-> method\n", "\n", "Use clean names, no prefixes. Return structural triples only.\n", "\"\"\".replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=enhanced_prompt,\n", "        allowed_nodes=['class', 'interface', 'method', 'variable', 'table', 'endpoint'],\n", "        allowed_relationships=[\n", "            ('class', 'declares', 'method'),\n", "            ('interface', 'declares', 'method'),\n", "            ('class', 'declares', 'endpoint'),\n", "            ('method', 'calls', 'method'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('class', 'uses', 'class'),\n", "            ('interface', 'extends', 'interface'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'maps_to', 'table'),\n", "            ('method', 'reads_from', 'table'),\n", "            ('method', 'writes_to', 'table')\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False\n", "    )\n", "    \n", "    try:\n", "        # Process only this chunk - no heavy memory usage\n", "        escaped_content = escape_braces_for_langchain(doc_info['content'])\n", "        doc = Document(page_content=escaped_content, metadata=doc_info['metadata'])\n", "        graph_docs = transformer.convert_to_graph_documents([doc])\n", "        \n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "                \n", "                # Validate relationship direction\n", "                valid_directions = {\n", "                    ('class', 'declares', 'method'),\n", "                    ('interface', 'declares', 'method'),\n", "                    ('class', 'declares', 'endpoint'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'uses', 'class'),\n", "                    ('interface', 'extends', 'interface'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('class', 'maps_to', 'table'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                }\n", "                \n", "                # Check if current direction is valid\n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    # Check if reverse direction is valid\n", "                    if (t_type, rel_type, s_type) in valid_directions:\n", "                        s_node, t_node = t_node, s_node\n", "                        s_type, t_type = t_type, s_type\n", "                    else:\n", "                        continue  # Skip invalid relationship\n", "\n", "                # Normalize entity names\n", "                s_node = extract_clean_name(s_node, s_type)\n", "                t_node = extract_clean_name(t_node, t_type)\n", "\n", "                # Filter out temp variables\n", "                if t_type == 'variable' and is_temp_variable(t_node):\n", "                    continue\n", "                if s_type == 'variable' and is_temp_variable(s_node):\n", "                    continue\n", "\n", "                if not s_node or not t_node or s_node == t_node:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type.title(),\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type.title(),\n", "                    'relationship': rel_type.upper(),\n", "                    'file_path': file_path\n", "                })\n", "                \n", "    except Exception as e:\n", "        print(f\"LLM processing error for {file_path}: {e}\")\n", "        continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "\n", "# Store Stage 4 results in memory for Stage 5 batch processing\n", "for _, row in df_llm_lineage.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "    \n", "    # Store variable contexts for Stage 5 batch processing\n", "    if row['destination_type'] == 'Variable':\n", "        memory['variable_contexts'][row['destination_node']] = {\n", "            'context': row['source_node'] if row['source_type'] in ['Method', 'Class'] else 'Unknown',\n", "            'context_type': row['source_type'].lower() if row['source_type'] in ['Method', 'Class'] else 'class',\n", "            'stage': '4b_structural'\n", "        }\n", "\n", "save_memory(memory)\n", "\n", "print(f'Stage 4B Complete: {len(df_llm_lineage)} structural relationships extracted')\n", "print(f'Memory usage: Lightweight - no heavy memory or registry used')\n", "print(f'Variable contexts stored for Stage 5: {len([r for _, r in df_llm_lineage.iterrows() if r[\"destination_type\"] == \"Variable\"])}')"]}, {"cell_type": "code", "execution_count": 201, "id": "b9fafc70", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Userrepository</td>\n", "      <td>Variable</td>\n", "      <td>HAS_FIELD</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>Userrepository</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>Userrepository</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>/Get/{Id}</td>\n", "      <td>Endpoint</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>User</td>\n", "      <td>Table</td>\n", "      <td>MAPS_TO</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Userutils</td>\n", "      <td>Class</td>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Userutils</td>\n", "      <td>Class</td>\n", "      <td>Generateusercode</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>User</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Generateusercode</td>\n", "      <td>Method</td>\n", "      <td>Base</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Generateusercode</td>\n", "      <td>Method</td>\n", "      <td>Code</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Orderservice</td>\n", "      <td>Class</td>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Orderservice</td>\n", "      <td>Class</td>\n", "      <td>Orderrepository</td>\n", "      <td>Variable</td>\n", "      <td>HAS_FIELD</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Orderrepository</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Orderservice</td>\n", "      <td>Class</td>\n", "      <td>Order</td>\n", "      <td>Table</td>\n", "      <td>MAPS_TO</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Orderutils</td>\n", "      <td>Class</td>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Orderutils</td>\n", "      <td>Class</td>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>Dto</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>Order</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>Price</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>Quantity</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>Total</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>USES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           source_node source_type    destination_node destination_type  \\\n", "0          Userservice       Class          Createuser           Method   \n", "1          Userservice       Class             Getuser           Method   \n", "2          Userservice       Class      Userrepository         Variable   \n", "3           Createuser      Method      Userrepository         Variable   \n", "4              Getuser      Method      Userrepository         Variable   \n", "5          Userservice       Class             /Create         Endpoint   \n", "6          Userservice       Class           /Get/{Id}         Endpoint   \n", "7          Userservice       Class                User            Table   \n", "8            Userutils       Class  Convertdtotoentity           Method   \n", "9            Userutils       Class    Generateusercode           Method   \n", "10  Convertdtotoentity      Method                User         Variable   \n", "11    Generateusercode      Method                Base         Variable   \n", "12    Generateusercode      Method                Code         Variable   \n", "13        Orderservice       Class         Createorder           Method   \n", "14        Orderservice       Class     Orderrepository         Variable   \n", "15         Createorder      Method     Orderrepository         Variable   \n", "16        Orderservice       Class               Order            Table   \n", "17          Orderutils       Class  Convertdtotoentity           Method   \n", "18          Orderutils       Class      Calculatetotal           Method   \n", "19  Convertdtotoentity      Method                 Dto         Variable   \n", "20  Convertdtotoentity      Method               Order         Variable   \n", "21      Calculatetotal      Method               Price         Variable   \n", "22      Calculatetotal      Method            Quantity         Variable   \n", "23      Calculatetotal      Method               Total         Variable   \n", "24      Calculatetotal      Method          Taxedtotal         Variable   \n", "\n", "   relationship                                          file_path  \n", "0      DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "1      DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "2     HAS_FIELD  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "3          USES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "4          USES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "5      DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "6      DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "7       MAPS_TO  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "8      DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "9      DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "10         USES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "11         USES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "12         USES  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...  \n", "13     DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "14    HAS_FIELD  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "15         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "16      MAPS_TO  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "17     DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "18     DECLARES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "19         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "20         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "21         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "22         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "23         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  \n", "24         USES  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...  "]}, "execution_count": 201, "metadata": {}, "output_type": "execute_result"}], "source": ["df_llm_lineage"]}, {"cell_type": "code", "execution_count": 202, "id": "stage5_batch_processing_setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 5 Setup: 4 classes split into 1 batches of 50\n", "Batch processing will prevent memory overflow for large codebases\n"]}], "source": ["# ========== STAGE 5: BATCH PROCESSING WITH MEMORY OPTIMIZATION ==========\n", "\n", "import gc\n", "\n", "# Batch configuration for scalability\n", "BATCH_SIZE = 50  # Process 50 classes at a time\n", "\n", "# Initialize long-term memory for persistent storage\n", "long_term_memory = {\n", "    'all_validated_edges': set(),\n", "    'global_variable_contexts': {},\n", "    'global_method_signatures': {},\n", "    'processed_classes': set()\n", "}\n", "\n", "def get_class_specific_memory(class_name, long_term_memory):\n", "    \"\"\"Extract only memory relevant to current class for lightweight processing\"\"\"\n", "    relevant_memory = {\n", "        'variable_contexts': {},\n", "        'method_signatures': {}\n", "    }\n", "    \n", "    # Only include memory related to current class\n", "    for var, context in long_term_memory.get('global_variable_contexts', {}).items():\n", "        if context.get('context') == class_name or context.get('class_context') == class_name:\n", "            relevant_memory['variable_contexts'][var] = context\n", "    \n", "    for method, info in long_term_memory.get('global_method_signatures', {}).items():\n", "        if info.get('class') == class_name:\n", "            relevant_memory['method_signatures'][method] = info\n", "    \n", "    return relevant_memory\n", "\n", "def build_lightweight_memory_context(relevant_memory):\n", "    \"\"\"Build memory context string from filtered memory\"\"\"\n", "    memory_context = ''\n", "    \n", "    if relevant_memory.get('variable_contexts'):\n", "        memory_context += f\"Relevant Variable Contexts: {len(relevant_memory['variable_contexts'])} variables\\n\"\n", "        for var_name, context in list(relevant_memory['variable_contexts'].items())[:3]:  # Show first 3\n", "            memory_context += f\"  - {var_name} (context: {context.get('context', 'Unknown')})\\n\"\n", "    \n", "    if relevant_memory.get('method_signatures'):\n", "        memory_context += f\"Relevant Methods: {len(relevant_memory['method_signatures'])} methods\\n\"\n", "    \n", "    return memory_context\n", "\n", "# Prepare class batches\n", "class_names = list(class_registry.keys())\n", "class_batches = [class_names[i:i+BATCH_SIZE] for i in range(0, len(class_names), BATCH_SIZE)]\n", "\n", "print(f\"Stage 5 Setup: {len(class_names)} classes split into {len(class_batches)} batches of {BATCH_SIZE}\")\n", "print(f\"Batch processing will prevent memory overflow for large codebases\")"]}, {"cell_type": "code", "execution_count": 203, "id": "stage5_batch_execution", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Processing batch 1/1 (4 classes)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Batch 1 Processing:   0%|          | 0/4 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Batch 1 Processing: 100%|██████████| 4/4 [00:49<00:00, 12.35s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Batch 1 complete: 37 relationships extracted\n", "Memory cleaned for batch 1\n", "\n", "Stage 5 Complete: 37 transformation relationships extracted\n", "Total classes processed: 4\n", "Total edges in long-term memory: 37\n", "\n", "Relationship breakdown:\n", "Transformations: 5, Flows: 4\n", "Produces: 6, DB Ops: 3\n", "Method Calls: 7\n", "Scalability: Processed in 1 batches with memory cleanup\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 5: BATCH EXECUTION WITH SHORT/LONG TERM MEMORY ==========\n", "\n", "all_transformation_relationships = []\n", "\n", "# Process each batch with memory optimization\n", "for batch_num, batch_classes in enumerate(class_batches):\n", "    print(f\"\\nProcessing batch {batch_num + 1}/{len(class_batches)} ({len(batch_classes)} classes)\")\n", "    \n", "    # Initialize short-term memory for this batch only\n", "    short_term_memory = {\n", "        'variable_contexts': {},\n", "        'method_signatures': {},\n", "        'current_batch_edges': set()\n", "    }\n", "    \n", "    batch_relationships = []\n", "    \n", "    # Process each class in the batch\n", "    for class_name in tqdm(batch_classes, desc=f\"Batch {batch_num + 1} Processing\"):\n", "        if class_name not in class_registry:\n", "            continue\n", "            \n", "        class_info = class_registry[class_name]\n", "        source_code = class_info['source_code']\n", "        escaped_source_code = escape_braces_for_langchain(source_code)\n", "        \n", "        # Get class-specific memory context (lightweight)\n", "        relevant_memory = get_class_specific_memory(class_name, long_term_memory)\n", "        memory_context = build_lightweight_memory_context(relevant_memory)\n", "        \n", "        try:\n", "            # Enhanced transformation prompt with lightweight memory context\n", "            transformation_prompt = f\"\"\"\n", "You are a Java data flow analysis engine for class: {class_name}\n", "\n", "LIGHTWEIGHT MEMORY CONTEXT:\n", "{memory_context}\n", "\n", "EXTRACT DATA FLOW RELATIONSHIPS:\n", "\n", "1. VARIABLE TRANSFORMATIONS & FLOWS:\n", "   - Variable -[FLOWS_TO]-> Variable (data passing)\n", "   - Variable -[TRANSFORMS_TO]-> Variable (data conversion)\n", "   - Method -[PRODUCES]-> Variable (method creates variable)\n", "\n", "2. DATABASE OPERATIONS:\n", "   - Method -[READS_FROM]-> Table (SELECT operations)\n", "   - Method -[WRITES_TO]-> Table (INSERT/UPDATE/DELETE)\n", "   - Variable -[PERSISTS_TO]-> Table (entity persistence)\n", "\n", "3. API RELATIONSHIPS:\n", "   - Class -[EXPOSES]-> Endpoint (REST endpoints)\n", "   - Method -[MAPS_TO]-> Endpoint (method-endpoint mapping)\n", "   - Endpoint -[ACCEPTS]-> Variable (request parameters)\n", "   - Endpoint -[RETURNS]-> Variable (response data)\n", "\n", "4. METHOD OPERATIONS:\n", "   - Method -[CALLS]-> Method (method invocations)\n", "   - Method -[INVOKES]-> ExternalService (external API calls)\n", "\n", "NAMING RULES:\n", "- PascalCase for Methods, Classes, Endpoints\n", "- Variables: only variable name (userDto, not CreateUser.userDto)\n", "- Endpoints: include HTTP method (GET:/api/users)\n", "- Tables: singular form (User, not Users)\n", "- Filter temp variables (i, j, temp, tmp, counter)\n", "\n", "CODE:\n", "{escaped_source_code}\n", "\n", "Return format: [SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\"\"\"\n", "            \n", "            response = llm.invoke(transformation_prompt)\n", "            content = response.content if hasattr(response, 'content') else str(response)\n", "            \n", "            doc = Document(page_content=content, metadata={'class_name': class_name})\n", "            \n", "            transformer = LLMGraphTransformer(\n", "                llm=llm,\n", "                allowed_nodes=['variable', 'method', 'table', 'class', 'endpoint', 'database', 'externalservice'],\n", "                allowed_relationships=[\n", "                    ('variable', 'flows_to', 'variable'),\n", "                    ('variable', 'transforms_to', 'variable'),\n", "                    ('method', 'produces', 'variable'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table'),\n", "                    ('variable', 'persists_to', 'table'),\n", "                    ('class', 'exposes', 'endpoint'),\n", "                    ('method', 'maps_to', 'endpoint'),\n", "                    ('endpoint', 'accepts', 'variable'),\n", "                    ('endpoint', 'returns', 'variable'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'invokes', 'externalservice')\n", "                ],\n", "                strict_mode=False,\n", "                node_properties=False,\n", "                relationship_properties=False,\n", "            )\n", "            \n", "            graph_docs = transformer.convert_to_graph_documents([doc])\n", "            \n", "            for gd in graph_docs:\n", "                for rel in gd.relationships:\n", "                    s_type = rel.source.type.title()\n", "                    t_type = rel.target.type.title()\n", "                    s_node = extract_clean_name(rel.source.id, s_type.lower())\n", "                    t_node = extract_clean_name(rel.target.id, t_type.lower())\n", "                    rel_type = rel.type.upper()\n", "                    \n", "                    # Filter out temp variables\n", "                    if s_type == 'Variable' and is_temp_variable(s_node):\n", "                        continue\n", "                    if t_type == 'Variable' and is_temp_variable(t_node):\n", "                        continue\n", "                    \n", "                    if not s_node or not t_node or s_node == t_node:\n", "                        continue\n", "                    \n", "                    relationship = {\n", "                        'source_node': s_node,\n", "                        'source_type': s_type,\n", "                        'destination_node': t_node,\n", "                        'destination_type': t_type,\n", "                        'relationship': rel_type,\n", "                        'class_context': class_name,\n", "                        'stage': 'batch_transformations'\n", "                    }\n", "                    \n", "                    batch_relationships.append(relationship)\n", "                    \n", "                    # Store in short-term memory for this batch\n", "                    edge_key = f\"{s_node}-{rel_type}-{t_node}\"\n", "                    short_term_memory['current_batch_edges'].add(edge_key)\n", "                    \n", "                    # Store variable context in short-term memory\n", "                    for node_name, node_type in [(s_node, s_type), (t_node, t_type)]:\n", "                        if node_type == 'Variable':\n", "                            short_term_memory['variable_contexts'][node_name] = {\n", "                                'context': class_name,\n", "                                'context_type': 'class',\n", "                                'stage': '5_batch_transformations'\n", "                            }\n", "            \n", "        except Exception as llm_error:\n", "            print(f\"LLM transformation error for {class_name}: {llm_error}\")\n", "            continue\n", "    \n", "    # Move batch results to long-term memory\n", "    long_term_memory['all_validated_edges'].update(short_term_memory['current_batch_edges'])\n", "    long_term_memory['global_variable_contexts'].update(short_term_memory['variable_contexts'])\n", "    long_term_memory['processed_classes'].update(batch_classes)\n", "    \n", "    # Add batch relationships to overall results\n", "    all_transformation_relationships.extend(batch_relationships)\n", "    \n", "    print(f\"Batch {batch_num + 1} complete: {len(batch_relationships)} relationships extracted\")\n", "    \n", "    # MEMORY CLEANUP - Critical for scalability\n", "    del short_term_memory\n", "    del batch_relationships\n", "    del relevant_memory\n", "    gc.collect()  # Force garbage collection\n", "    \n", "    print(f\"Memory cleaned for batch {batch_num + 1}\")\n", "\n", "# Create final DataFrame\n", "df_transformations = pd.DataFrame(all_transformation_relationships)\n", "\n", "print(f'\\nStage 5 Complete: {len(df_transformations)} transformation relationships extracted')\n", "print(f'Total classes processed: {len(long_term_memory[\"processed_classes\"])}')\n", "print(f'Total edges in long-term memory: {len(long_term_memory[\"all_validated_edges\"])}')\n", "\n", "# Summary statistics\n", "transformations_count = len([r for r in all_transformation_relationships if 'TRANSFORMS_TO' in r.get('relationship', '')])\n", "flows_count = len([r for r in all_transformation_relationships if 'FLOWS_TO' in r.get('relationship', '')])\n", "produces_count = len([r for r in all_transformation_relationships if 'PRODUCES' in r.get('relationship', '')])\n", "db_ops_count = len([r for r in all_transformation_relationships if r.get('relationship') in ['READS_FROM', 'WRITES_TO']])\n", "method_calls_count = len([r for r in all_transformation_relationships if 'CALLS' in r.get('relationship', '')])\n", "\n", "print(f'\\nRelationship breakdown:')\n", "print(f'Transformations: {transformations_count}, Flows: {flows_count}')\n", "print(f'Produces: {produces_count}, DB Ops: {db_ops_count}')\n", "print(f'Method Calls: {method_calls_count}')\n", "print(f'Scalability: Processed in {len(class_batches)} batches with memory cleanup')"]}, {"cell_type": "code", "execution_count": 204, "id": "da52ce2c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>class_context</th>\n", "      <th>stage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Userdto</td>\n", "      <td>Variable</td>\n", "      <td>User</td>\n", "      <td>Variable</td>\n", "      <td>TRANSFORMS_TO</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>User</td>\n", "      <td>Table</td>\n", "      <td>WRITES_TO</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>User</td>\n", "      <td>Table</td>\n", "      <td>READS_FROM</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Post:/User/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>EXPOSES</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Get:/User/Get/{Id}</td>\n", "      <td>Endpoint</td>\n", "      <td>EXPOSES</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>Post:/User/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>MAPS_TO</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>Userdto</td>\n", "      <td>Variable</td>\n", "      <td>ACCEPTS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>User</td>\n", "      <td>Variable</td>\n", "      <td>RETURNS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>Get:/User/Get/{Id}</td>\n", "      <td>Endpoint</td>\n", "      <td>MAPS_TO</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>Id</td>\n", "      <td>Variable</td>\n", "      <td>ACCEPTS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>User</td>\n", "      <td>Variable</td>\n", "      <td>RETURNS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>Save</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>Findbyid</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>UserService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Name</td>\n", "      <td>Variable</td>\n", "      <td>Base</td>\n", "      <td>Variable</td>\n", "      <td>FLOWS_TO</td>\n", "      <td>UserUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Email</td>\n", "      <td>Variable</td>\n", "      <td>Base</td>\n", "      <td>Variable</td>\n", "      <td>FLOWS_TO</td>\n", "      <td>UserUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Base</td>\n", "      <td>Variable</td>\n", "      <td>Code</td>\n", "      <td>Variable</td>\n", "      <td>TRANSFORMS_TO</td>\n", "      <td>UserUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Code</td>\n", "      <td>Variable</td>\n", "      <td>Output</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>UserUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>User</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>UserUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Generateusercode</td>\n", "      <td>Method</td>\n", "      <td>Code</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>UserUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Orderdto</td>\n", "      <td>Variable</td>\n", "      <td>Order</td>\n", "      <td>Variable</td>\n", "      <td>TRANSFORMS_TO</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>User</td>\n", "      <td>Variable</td>\n", "      <td>Usercode</td>\n", "      <td>Variable</td>\n", "      <td>TRANSFORMS_TO</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Order</td>\n", "      <td>Table</td>\n", "      <td>WRITES_TO</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Orderservice</td>\n", "      <td>Class</td>\n", "      <td>Post:/Order/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>EXPOSES</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Post:/Order/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>MAPS_TO</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Post:/Order/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>Orderdto</td>\n", "      <td>Variable</td>\n", "      <td>ACCEPTS</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Post:/Order/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>Order</td>\n", "      <td>Variable</td>\n", "      <td>RETURNS</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Generateusercode</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Createorder</td>\n", "      <td>Method</td>\n", "      <td>Save</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>OrderService</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Price</td>\n", "      <td>Variable</td>\n", "      <td>Total</td>\n", "      <td>Variable</td>\n", "      <td>FLOWS_TO</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Quantity</td>\n", "      <td>Variable</td>\n", "      <td>Total</td>\n", "      <td>Variable</td>\n", "      <td>FLOWS_TO</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Total</td>\n", "      <td>Variable</td>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>TRANSFORMS_TO</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>Output</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>Order</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           source_node source_type    destination_node destination_type  \\\n", "0              Userdto    Variable                User         Variable   \n", "1           Createuser      Method                User            Table   \n", "2              Getuser      Method                User            Table   \n", "3          Userservice       Class   Post:/User/Create         Endpoint   \n", "4          Userservice       Class  Get:/User/Get/{Id}         Endpoint   \n", "5           Createuser      Method   Post:/User/Create         Endpoint   \n", "6           Createuser      Method             Userdto         Variable   \n", "7           Createuser      Method                User         Variable   \n", "8              Getuser      Method  Get:/User/Get/{Id}         Endpoint   \n", "9              Getuser      Method                  Id         Variable   \n", "10             Getuser      Method                User         Variable   \n", "11          Createuser      Method  Convertdtotoentity           Method   \n", "12          Createuser      Method                Save           Method   \n", "13             Getuser      Method            Findbyid           Method   \n", "14                Name    Variable                Base         Variable   \n", "15               Email    Variable                Base         Variable   \n", "16                Base    Variable                Code         Variable   \n", "17                Code    Variable              Output         Variable   \n", "18  Convertdtotoentity      Method                User         Variable   \n", "19    Generateusercode      Method                Code         Variable   \n", "20            Orderdto    Variable               Order         Variable   \n", "21                User    Variable            Usercode         Variable   \n", "22         Createorder      Method               Order            Table   \n", "23        Orderservice       Class  Post:/Order/Create         Endpoint   \n", "24         Createorder      Method  Post:/Order/Create         Endpoint   \n", "25  Post:/Order/Create    Endpoint            Orderdto         Variable   \n", "26  Post:/Order/Create    Endpoint               Order         Variable   \n", "27         Createorder      Method  Convertdtotoentity           Method   \n", "28         Createorder      Method             Getuser           Method   \n", "29         Createorder      Method    Generateusercode           Method   \n", "30         Createorder      Method                Save           Method   \n", "31               Price    Variable               Total         Variable   \n", "32            Quantity    Variable               Total         Variable   \n", "33               Total    Variable          Taxedtotal         Variable   \n", "34          Taxedtotal    Variable              Output         Variable   \n", "35  Convertdtotoentity      Method               Order         Variable   \n", "36      Calculatetotal      Method          Taxedtotal         Variable   \n", "\n", "     relationship class_context                  stage  \n", "0   TRANSFORMS_TO   UserService  batch_transformations  \n", "1       WRITES_TO   UserService  batch_transformations  \n", "2      READS_FROM   UserService  batch_transformations  \n", "3         EXPOSES   UserService  batch_transformations  \n", "4         EXPOSES   UserService  batch_transformations  \n", "5         MAPS_TO   UserService  batch_transformations  \n", "6         ACCEPTS   UserService  batch_transformations  \n", "7         RETURNS   UserService  batch_transformations  \n", "8         MAPS_TO   UserService  batch_transformations  \n", "9         ACCEPTS   UserService  batch_transformations  \n", "10        RETURNS   UserService  batch_transformations  \n", "11          CALLS   UserService  batch_transformations  \n", "12          CALLS   UserService  batch_transformations  \n", "13          CALLS   UserService  batch_transformations  \n", "14       FLOWS_TO     UserUtils  batch_transformations  \n", "15       FLOWS_TO     UserUtils  batch_transformations  \n", "16  TRANSFORMS_TO     UserUtils  batch_transformations  \n", "17       PRODUCES     UserUtils  batch_transformations  \n", "18       PRODUCES     UserUtils  batch_transformations  \n", "19       PRODUCES     UserUtils  batch_transformations  \n", "20  TRANSFORMS_TO  OrderService  batch_transformations  \n", "21  TRANSFORMS_TO  OrderService  batch_transformations  \n", "22      WRITES_TO  OrderService  batch_transformations  \n", "23        EXPOSES  OrderService  batch_transformations  \n", "24        MAPS_TO  OrderService  batch_transformations  \n", "25        ACCEPTS  OrderService  batch_transformations  \n", "26        RETURNS  OrderService  batch_transformations  \n", "27          CALLS  OrderService  batch_transformations  \n", "28          CALLS  OrderService  batch_transformations  \n", "29          CALLS  OrderService  batch_transformations  \n", "30          CALLS  OrderService  batch_transformations  \n", "31       FLOWS_TO    OrderUtils  batch_transformations  \n", "32       FLOWS_TO    OrderUtils  batch_transformations  \n", "33  TRANSFORMS_TO    OrderUtils  batch_transformations  \n", "34       PRODUCES    OrderUtils  batch_transformations  \n", "35       PRODUCES    OrderUtils  batch_transformations  \n", "36       PRODUCES    OrderUtils  batch_transformations  "]}, "execution_count": 204, "metadata": {}, "output_type": "execute_result"}], "source": ["df_transformations"]}, {"cell_type": "code", "execution_count": 205, "id": "795e447b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hierarchy relationships: 10\n", "  - CONTAINS (folder-file): 6\n", "  - DECLARES (file-class): 4\n", "  - File-Class DECLARES in hierarchy: 4\n", "    Sample file-class relationships:\n", "      UserService (File) -[DECLARES]-> UserService (Class)\n", "      UserUtils (File) -[DECLARES]-> UserUtils (Class)\n", "      OrderService (File) -[DECLARES]-> OrderService (Class)\n", "\n", "🔍 DEBUG: ALL hierarchy relationships (10):\n", "  LineageTestProject (Folder) -[CONTAINS]-> AppOne (Folder)\n", "  AppOne (Folder) -[CONTAINS]-> UserService (File)\n", "  AppOne (Folder) -[CONTAINS]-> UserUtils (File)\n", "  LineageTestProject (Folder) -[CONTAINS]-> AppTwo (Folder)\n", "  AppTwo (Folder) -[CONTAINS]-> OrderService (File)\n", "  AppTwo (Folder) -[CONTAINS]-> OrderUtils (File)\n", "  UserService (File) -[DECLARES]-> UserService (Class)\n", "  UserUtils (File) -[DECLARES]-> UserUtils (Class)\n", "  OrderService (File) -[DECLARES]-> OrderService (Class)\n", "  OrderUtils (File) -[DECLARES]-> OrderUtils (Class)\n", "LLM lineage relationships: 25\n", "Transformation relationships: 37\n"]}], "source": ["# ========== STAGE 6: FINAL CONSOLIDATION ==========\n", "\n", "# Combine all DataFrames\n", "all_dataframes = []\n", "\n", "if len(df_hierarchy) > 0:\n", "    all_dataframes.append(df_hierarchy)\n", "    print(f\"Hierarchy relationships: {len(df_hierarchy)}\")\n", "    \n", "    # DEBUG: Show breakdown of hierarchy relationships\n", "    declares_count = len(df_hierarchy[df_hierarchy['relationship'] == 'DECLARES'])\n", "    contains_count = len(df_hierarchy[df_hierarchy['relationship'] == 'CONTAINS'])\n", "    print(f\"  - CONTAINS (folder-file): {contains_count}\")\n", "    print(f\"  - DECLARES (file-class): {declares_count}\")\n", "    \n", "    # DEBUG: Show actual file-class relationships in df_hierarchy\n", "    file_class_in_hierarchy = df_hierarchy[\n", "        (df_hierarchy['source_type'] == 'File') & \n", "        (df_hierarchy['destination_type'] == 'Class') & \n", "        (df_hierarchy['relationship'] == 'DECLARES')\n", "    ]\n", "    print(f\"  - File-Class DECLARES in hierarchy: {len(file_class_in_hierarchy)}\")\n", "    if len(file_class_in_hierarchy) > 0:\n", "        print(\"    Sample file-class relationships:\")\n", "        for _, row in file_class_in_hierarchy.head(3).iterrows():\n", "            print(f\"      {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})\")\n", "    \n", "    # DEBUG: Show ALL hierarchy relationships to see what's actually there\n", "    print(f\"\\n🔍 DEBUG: ALL hierarchy relationships ({len(df_hierarchy)}):\")\n", "    for _, row in df_hierarchy.iterrows():\n", "        print(f\"  {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})\")\n", "\n", "if len(df_llm_lineage) > 0:\n", "    all_dataframes.append(df_llm_lineage)\n", "    print(f\"LLM lineage relationships: {len(df_llm_lineage)}\")\n", "\n", "if len(df_transformations) > 0:\n", "    all_dataframes.append(df_transformations)\n", "    print(f\"Transformation relationships: {len(df_transformations)}\")\n", "\n", "# Consolidate all relationships\n", "if all_dataframes:\n", "    df_final = pd.concat(all_dataframes, ignore_index=True)\n", "else:\n", "    df_final = pd.DataFrame()\n", "\n", "# Remove duplicates based on core relationship columns\n", "df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])"]}, {"cell_type": "code", "execution_count": 206, "id": "1da65ec8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 DEBUG: File-Class relationships after deduplication: 4\n"]}], "source": ["# DEBUG: Check file-class relationships after deduplication\n", "file_class_after_dedup = df_final[\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", "]\n", "print(f\"\\n🔍 DEBUG: File-Class relationships after deduplication: {len(file_class_after_dedup)}\")"]}, {"cell_type": "code", "execution_count": 207, "id": "95029d0c", "metadata": {}, "outputs": [], "source": ["# Filter to only allowed nodes and relationships\n", "allowed_nodes = {'Folder', 'File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint', 'Database', 'Externalservice'}\n", "allowed_relationships = {\n", "    'CONTAINS', 'DECLARES', 'HAS_FIELD', 'USES', 'CALLS', 'EXTENDS', 'IMPLEMENTS',\n", "    'MAPS_TO', 'READS_FROM', 'WRITES_TO', 'FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES',\n", "    'EXPOSES', 'ACCEPTS', 'RETURNS', 'INVOKES', 'PERSISTS_TO'\n", "}\n", "\n", "# Apply filters\n", "df_final = df_final[\n", "    (df_final['source_type'].isin(allowed_nodes)) &\n", "    (df_final['destination_type'].isin(allowed_nodes)) &\n", "    (df_final['relationship'].isin(allowed_relationships))\n", "]"]}, {"cell_type": "code", "execution_count": 208, "id": "47c7e6b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 DEBUG: File-Class relationships after filtering: 4\n", "\n", "🔧 NORMALIZING: Applying consistent PascalCase to all node names...\n", "✅ NORMALIZATION: Applied consistent PascalCase to 72 relationships\n"]}], "source": ["# DEBUG: Check file-class relationships after filtering\n", "file_class_after_filter = df_final[\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", "]\n", "print(f\"🔍 DEBUG: File-Class relationships after filtering: {len(file_class_after_filter)}\")\n", "\n", "# CRITICAL FIX: Normalize all node names to consistent PascalCase\n", "# This fixes issues like 'Userservice' vs 'UserService'\n", "print(\"\\n🔧 NORMALIZING: Applying consistent PascalCase to all node names...\")\n", "\n", "def normalize_node_name(name, node_type):\n", "    \"\"\"Ensure consistent PascalCase for folders, files, classes, methods\"\"\"\n", "    if not name or pd.isna(name):\n", "        return name\n", "    \n", "    # Apply PascalCase normalization for these types\n", "    if node_type in ['Folder', 'File', 'Class', 'Interface', 'Method', 'Table']:\n", "        return to_pascal_case(str(name))\n", "    \n", "    # Keep variables as-is (they should not be PascalCase)\n", "    return str(name)\n", "\n", "# Apply normalization\n", "df_final['source_node'] = df_final.apply(lambda row: normalize_node_name(row['source_node'], row['source_type']), axis=1)\n", "df_final['destination_node'] = df_final.apply(lambda row: normalize_node_name(row['destination_node'], row['destination_type']), axis=1)\n", "\n", "print(f\"✅ NORMALIZATION: Applied consistent PascalCase to {len(df_final)} relationships\")\n", "\n", "# Remove duplicates again after normalization (in case normalization created duplicates)\n", "before_dedup = len(df_final)\n", "df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "after_dedup = len(df_final)\n", "if before_dedup != after_dedup:\n", "    print(f\"🔧 DEDUPLICATION: Removed {before_dedup - after_dedup} duplicate relationships after normalization\")"]}, {"cell_type": "code", "execution_count": 209, "id": "d8b2ba29", "metadata": {}, "outputs": [], "source": ["\n", "# Clean up column names and ensure consistency\n", "required_columns = ['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']\n", "for col in required_columns:\n", "    if col not in df_final.columns:\n", "        df_final[col] = ''"]}, {"cell_type": "code", "execution_count": 210, "id": "e05666b9", "metadata": {}, "outputs": [], "source": ["# Final cleaning and validation\n", "df_final = df_final[df_final['source_node'].notna() & (df_final['source_node'] != '')]\n", "df_final = df_final[df_final['destination_node'].notna() & (df_final['destination_node'] != '')]"]}, {"cell_type": "code", "execution_count": 211, "id": "a434be19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 DEBUG: File-Class relationships after filtering: 4\n"]}], "source": ["file_class_after_filter = df_final[\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", "]\n", "print(f\"🔍 DEBUG: File-Class relationships after filtering: {len(file_class_after_filter)}\")"]}, {"cell_type": "code", "execution_count": 212, "id": "1142ad54", "metadata": {}, "outputs": [], "source": ["# CRITICAL FIX: Don't filter out File-Class DECLARES relationships even if they have the same name\n", "# This is valid: UserService.java (File) -[DECLARES]-> UserService (Class)\n", "file_class_declares = (\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", ")\n", "\n", "# Filter out self-referential relationships EXCEPT for valid File-Class DECLARES\n", "df_final = df_final[\n", "    (df_final['source_node'] != df_final['destination_node']) | file_class_declares\n", "]"]}, {"cell_type": "code", "execution_count": 213, "id": "94c3c5a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 FIXED: File-Class relationships after final cleaning: 4\n", "✅ File-Class relationships preserved:\n", "  UserService (File) -[DECLARES]-> UserService (Class)\n", "  UserUtils (File) -[DECLARES]-> UserUtils (Class)\n", "  OrderService (File) -[DECLARES]-> OrderService (Class)\n", "  OrderUtils (File) -[DECLARES]-> OrderUtils (Class)\n"]}], "source": ["# DEBUG: Check file-class relationships after final cleaning\n", "file_class_final = df_final[\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", "]\n", "print(f\"🎯 FIXED: File-Class relationships after final cleaning: {len(file_class_final)}\")\n", "if len(file_class_final) > 0:\n", "    print(\"✅ File-Class relationships preserved:\")\n", "    for _, row in file_class_final.iterrows():\n", "        print(f\"  {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})\")"]}, {"cell_type": "code", "execution_count": 214, "id": "ec3e80ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Final consolidation complete:\n", "Total relationships: 72\n", "CSV saved: java_lineage_test_apps.csv\n"]}], "source": ["# Save to CSV\n", "csv_filename = 'java_lineage_test_apps.csv'\n", "df_final[required_columns].to_csv(csv_filename, index=False)\n", "\n", "print(f\"\\nFinal consolidation complete:\")\n", "print(f\"Total relationships: {len(df_final)}\")\n", "print(f\"CSV saved: {csv_filename}\")"]}, {"cell_type": "code", "execution_count": 215, "id": "436ed4cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Relationship breakdown:\n", "  DECLARES: 13\n", "  USES: 12\n", "  CALLS: 7\n", "  CONTAINS: 6\n", "  PRODUCES: 6\n", "  MAPS_TO: 5\n", "  TRANSFORMS_TO: 5\n", "  FLOWS_TO: 4\n", "  EXPOSES: 3\n", "  ACCEPTS: 3\n", "  RETURNS: 3\n", "  HAS_FIELD: 2\n", "  WRITES_TO: 2\n", "  READS_FROM: 1\n"]}], "source": ["\n", "# Summary by relationship type\n", "relationship_summary = df_final['relationship'].value_counts()\n", "print(\"\\nRelationship breakdown:\")\n", "for rel_type, count in relationship_summary.items():\n", "    print(f\"  {rel_type}: {count}\")"]}, {"cell_type": "code", "execution_count": 216, "id": "stage6_final_consolidation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Node type summary:\n", "  Class: 20\n", "  Endpoint: 10\n", "  File: 8\n", "  Folder: 8\n", "  Method: 47\n", "  Table: 0\n", "  Variable: 46\n"]}], "source": ["\n", "# Summary by node type\n", "print(\"\\nNode type summary:\")\n", "source_types = df_final['source_type'].value_counts()\n", "dest_types = df_final['destination_type'].value_counts()\n", "all_types = (source_types + dest_types).fillna(0).astype(int)\n", "for node_type, count in all_types.items():\n", "    print(f\"  {node_type}: {count}\")"]}, {"cell_type": "code", "execution_count": 217, "id": "0a33e529", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>class_context</th>\n", "      <th>stage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>LineageTestProject</td>\n", "      <td>Folder</td>\n", "      <td>AppOne</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AppOne</td>\n", "      <td>Folder</td>\n", "      <td>UserService</td>\n", "      <td>File</td>\n", "      <td>CONTAINS</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AppOne</td>\n", "      <td>Folder</td>\n", "      <td>UserUtils</td>\n", "      <td>File</td>\n", "      <td>CONTAINS</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>LineageTestProject</td>\n", "      <td>Folder</td>\n", "      <td>AppTwo</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AppTwo</td>\n", "      <td>Folder</td>\n", "      <td>OrderService</td>\n", "      <td>File</td>\n", "      <td>CONTAINS</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>Quantity</td>\n", "      <td>Variable</td>\n", "      <td>Total</td>\n", "      <td>Variable</td>\n", "      <td>FLOWS_TO</td>\n", "      <td>NaN</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>Total</td>\n", "      <td>Variable</td>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>TRANSFORMS_TO</td>\n", "      <td>NaN</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>Output</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>NaN</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>Convertdtotoentity</td>\n", "      <td>Method</td>\n", "      <td>Order</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>NaN</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>Calculatetotal</td>\n", "      <td>Method</td>\n", "      <td>Taxedtotal</td>\n", "      <td>Variable</td>\n", "      <td>PRODUCES</td>\n", "      <td>NaN</td>\n", "      <td>OrderUtils</td>\n", "      <td>batch_transformations</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>72 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           source_node source_type destination_node destination_type  \\\n", "0   LineageTestProject      Folder           AppOne           Folder   \n", "1               AppOne      Folder      UserService             File   \n", "2               AppOne      Folder        UserUtils             File   \n", "3   LineageTestProject      Folder           AppTwo           Folder   \n", "4               AppTwo      Folder     OrderService             File   \n", "..                 ...         ...              ...              ...   \n", "67            Quantity    Variable            Total         Variable   \n", "68               Total    Variable       Taxedtotal         Variable   \n", "69          Taxedtotal    Variable           Output         Variable   \n", "70  Convertdtotoentity      Method            Order         Variable   \n", "71      Calculatetotal      Method       Taxedtotal         Variable   \n", "\n", "     relationship                                          file_path  \\\n", "0        CONTAINS                                                NaN   \n", "1        CONTAINS  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...   \n", "2        CONTAINS  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...   \n", "3        CONTAINS                                                NaN   \n", "4        CONTAINS  C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\Orde...   \n", "..            ...                                                ...   \n", "67       FLOWS_TO                                                NaN   \n", "68  TRANSFORMS_TO                                                NaN   \n", "69       PRODUCES                                                NaN   \n", "70       PRODUCES                                                NaN   \n", "71       PRODUCES                                                NaN   \n", "\n", "   class_context                  stage  \n", "0            NaN                    NaN  \n", "1            NaN                    NaN  \n", "2            NaN                    NaN  \n", "3            NaN                    NaN  \n", "4            NaN                    NaN  \n", "..           ...                    ...  \n", "67    OrderUtils  batch_transformations  \n", "68    OrderUtils  batch_transformations  \n", "69    OrderUtils  batch_transformations  \n", "70    OrderUtils  batch_transformations  \n", "71    OrderUtils  batch_transformations  \n", "\n", "[72 rows x 8 columns]"]}, "execution_count": 217, "metadata": {}, "output_type": "execute_result"}], "source": ["df_final"]}, {"cell_type": "code", "execution_count": 218, "id": "stage7_neo4j_upload", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 DEBUG: File-Class relationships BEFORE Neo4j upload: 4\n", "File-Class relationships to upload:\n", "  UserService (File) -[DECLARES]-> UserService (Class)\n", "  UserUtils (File) -[DECLARES]-> UserUtils (Class)\n", "  OrderService (File) -[DECLARES]-> OrderService (Class)\n", "  OrderUtils (File) -[DECLARES]-> OrderUtils (Class)\n", "\n", "🔧 Final normalization: Converting all folder/file/class/method names to PascalCase before Neo4j upload...\n", "✅ Final normalization: Made 0 name corrections\n", "Creating 48 unique (name, type) nodes...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Creating nodes: 100%|██████████| 48/48 [00:00<00:00, 99.66it/s] \n", "Creating relationships: 100%|██████████| 72/72 [00:00<00:00, 90.67it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Neo4j upload complete: 48 unique nodes, 72 relationships\n", "📊 Node breakdown by type:\n", "  Class: 8 nodes\n", "  Endpoint: 5 nodes\n", "  File: 4 nodes\n", "  Folder: 3 nodes\n", "  Method: 8 nodes\n", "  Table: 2 nodes\n", "  Variable: 18 nodes\n", "\n", "========== PIPELINE COMPLETE ==========\n", "Total processing stages: 7\n", "Final relationships: 72\n", "Memory file: lineage_memory_v9.json\n", "CSV output: java_lineage_v9.csv\n", "Neo4j database: test\n", "\n", "🔍 DEBUG: File-Class relationships in final output: 4\n", "Sample file-class relationships:\n", "  UserService -[DECLARES]-> UserService\n", "  UserUtils -[DECLARES]-> UserUtils\n", "  OrderService -[DECLARES]-> OrderService\n", "  OrderUtils -[DECLARES]-> OrderUtils\n", "========================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 7: NEO4J UPLOAD ==========\n", "\n", "def upload_to_neo4j(df_final):\n", "    # Use (name, type) tuples to handle nodes with same name but different types\n", "    # This prevents nodes from being overwritten when they have the same name but different types\n", "    unique_nodes = set()  # Set of (node_name, node_type) tuples\n", "    \n", "    for _, row in df_final.iterrows():\n", "        source_node = row['source_node']\n", "        dest_node = row['destination_node']\n", "        source_type = row['source_type']\n", "        dest_type = row['destination_type']\n", "        \n", "        # Store as (name, type) tuples to handle same names with different types\n", "        unique_nodes.add((source_node, source_type))\n", "        unique_nodes.add((dest_node, dest_type))\n", "    \n", "    print(f\"Creating {len(unique_nodes)} unique (name, type) nodes...\")\n", "    \n", "    # Create nodes with proper labels and metadata\n", "    for node_name, node_type in tqdm(unique_nodes, desc=\"Creating nodes\"):\n", "        \n", "        # Add variable context metadata if available\n", "        metadata = {}\n", "        if node_type == 'Variable' and node_name in memory.get('variable_contexts', {}):\n", "            context_info = memory['variable_contexts'][node_name]\n", "            metadata['defining_context'] = context_info.get('context', 'Unknown')\n", "            metadata['context_type'] = context_info.get('context_type', 'Unknown')\n", "        \n", "        # Create node with metadata\n", "        properties_str = ', '.join([f\"{k}: '{v}'\" for k, v in metadata.items()])\n", "        properties_clause = f\", {properties_str}\" if properties_str else \"\"\n", "        \n", "        create_query = f\"\"\"\n", "        MERGE (n:{node_type} {{name: '{node_name}'{properties_clause}}})\n", "        \"\"\"\n", "        \n", "        try:\n", "            graph.query(create_query)\n", "        except Exception as e:\n", "            print(f\"Error creating node {node_name} ({node_type}): {e}\")\n", "    \n", "    # Create relationships\n", "    for _, row in tqdm(df_final.iterrows(), desc=\"Creating relationships\", total=len(df_final)):\n", "        source_node = row['source_node']\n", "        dest_node = row['destination_node']\n", "        source_type = row['source_type']\n", "        dest_type = row['destination_type']\n", "        relationship = row['relationship']\n", "        \n", "        create_rel_query = f\"\"\"\n", "        MATCH (s:{source_type} {{name: '{source_node}'}})\n", "        MATCH (t:{dest_type} {{name: '{dest_node}'}})\n", "        MERGE (s)-[:{relationship}]->(t)\n", "        \"\"\"\n", "        \n", "        try:\n", "            graph.query(create_rel_query)\n", "        except Exception as e:\n", "            print(f\"Error creating relationship {source_node}-{relationship}->{dest_node}: {e}\")\n", "    \n", "    print(f\"✅ Neo4j upload complete: {len(unique_nodes)} unique nodes, {len(df_final)} relationships\")\n", "    print(f\"📊 Node breakdown by type:\")\n", "    type_counts = {}\n", "    for _, node_type in unique_nodes:\n", "        type_counts[node_type] = type_counts.get(node_type, 0) + 1\n", "    for node_type, count in sorted(type_counts.items()):\n", "        print(f\"  {node_type}: {count} nodes\")\n", "\n", "# DEBUG: Final check BEFORE Neo4j upload\n", "file_class_before_upload = df_final[\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", "]\n", "print(f\"\\n🔍 DEBUG: File-Class relationships BEFORE Neo4j upload: {len(file_class_before_upload)}\")\n", "if len(file_class_before_upload) > 0:\n", "    print(\"File-Class relationships to upload:\")\n", "    for _, row in file_class_before_upload.iterrows():\n", "        print(f\"  {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})\")\n", "\n", "# Final normalization: Apply PascalCase to all node names before Neo4j upload\n", "print(\"\\n🔧 Final normalization: Converting all folder/file/class/method names to PascalCase before Neo4j upload...\")\n", "\n", "def final_pascal_case_normalization(name, node_type):\n", "    \"\"\"Final PascalCase normalization before Neo4j upload\"\"\"\n", "    if not name or pd.isna(name):\n", "        return name\n", "    \n", "    name_str = str(name).strip()\n", "    if not name_str:\n", "        return name_str\n", "    \n", "    # Apply PascalCase to these node types\n", "    if node_type in ['Folder', 'File', 'Class', 'Interface', 'Method', 'Table']:\n", "        return to_pascal_case(name_str)\n", "    \n", "    # Keep variables and other types as-is\n", "    return name_str\n", "\n", "# Apply final normalization to df_final\n", "original_source_names = df_final['source_node'].tolist()\n", "original_dest_names = df_final['destination_node'].tolist()\n", "\n", "df_final['source_node'] = df_final.apply(lambda row: final_pascal_case_normalization(row['source_node'], row['source_type']), axis=1)\n", "df_final['destination_node'] = df_final.apply(lambda row: final_pascal_case_normalization(row['destination_node'], row['destination_type']), axis=1)\n", "\n", "# Check for changes\n", "changes_made = 0\n", "for i, (orig_src, orig_dest) in enumerate(zip(original_source_names, original_dest_names)):\n", "    new_src = df_final.iloc[i]['source_node']\n", "    new_dest = df_final.iloc[i]['destination_node']\n", "    if orig_src != new_src:\n", "        print(f\"  📝 Normalized: '{orig_src}' → '{new_src}' ({df_final.iloc[i]['source_type']})\")\n", "        changes_made += 1\n", "    if orig_dest != new_dest:\n", "        print(f\"  📝 Normalized: '{orig_dest}' → '{new_dest}' ({df_final.iloc[i]['destination_type']})\")\n", "        changes_made += 1\n", "\n", "print(f\"✅ Final normalization: Made {changes_made} name corrections\")\n", "\n", "# Remove any duplicates created by normalization\n", "before_final_dedup = len(df_final)\n", "df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "after_final_dedup = len(df_final)\n", "if before_final_dedup != after_final_dedup:\n", "    print(f\"🔧 Final deduplication: Removed {before_final_dedup - after_final_dedup} duplicate relationships\")\n", "\n", "# Execute Neo4j upload\n", "upload_to_neo4j(df_final)\n", "\n", "# Final memory save\n", "save_memory(memory)\n", "\n", "print(\"\\n========== PIPELINE COMPLETE ==========\")\n", "print(f\"Total processing stages: 7\")\n", "print(f\"Final relationships: {len(df_final)}\")\n", "print(f\"Memory file: {MEMORY_FILE}\")\n", "print(f\"CSV output: java_lineage_v9.csv\")\n", "print(f\"Neo4j database: {NEO4J_DB}\")\n", "\n", "# DEBUG: Verify file-class relationships in final output\n", "file_class_in_final = df_final[\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", "]\n", "print(f\"\\n🔍 DEBUG: File-Class relationships in final output: {len(file_class_in_final)}\")\n", "if len(file_class_in_final) > 0:\n", "    print(\"Sample file-class relationships:\")\n", "    for _, row in file_class_in_final.head(5).iterrows():\n", "        print(f\"  {row['source_node']} -[DECLARES]-> {row['destination_node']}\")\n", "else:\n", "    print(\"❌ NO file-class relationships found in final output!\")\n", "    print(\"\\nChecking what relationships ARE in final output:\")\n", "    rel_summary = df_final['relationship'].value_counts()\n", "    for rel, count in rel_summary.head(10).items():\n", "        print(f\"  {rel}: {count}\")\n", "\n", "print(\"========================================\")"]}, {"cell_type": "code", "execution_count": 219, "id": "c29e2dce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 72 entries, 0 to 71\n", "Data columns (total 8 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   source_node       72 non-null     object\n", " 1   source_type       72 non-null     object\n", " 2   destination_node  72 non-null     object\n", " 3   destination_type  72 non-null     object\n", " 4   relationship      72 non-null     object\n", " 5   file_path         33 non-null     object\n", " 6   class_context     37 non-null     object\n", " 7   stage             37 non-null     object\n", "dtypes: object(8)\n", "memory usage: 4.6+ KB\n"]}], "source": ["df_final.info()"]}, {"cell_type": "code", "execution_count": 220, "id": "2ac2d9a5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>class_context</th>\n", "      <th>stage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Createuser</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Getuser</td>\n", "      <td>Method</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>Userrepository</td>\n", "      <td>Variable</td>\n", "      <td>HAS_FIELD</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>/Create</td>\n", "      <td>Endpoint</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Userservice</td>\n", "      <td>Class</td>\n", "      <td>/Get/{Id}</td>\n", "      <td>Endpoint</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    source_node source_type destination_node destination_type relationship  \\\n", "10  Userservice       Class       Createuser           Method     DECLARES   \n", "11  Userservice       Class          Getuser           Method     DECLARES   \n", "12  Userservice       Class   Userrepository         Variable    HAS_FIELD   \n", "15  Userservice       Class          /Create         Endpoint     DECLARES   \n", "16  Userservice       Class        /Get/{Id}         Endpoint     DECLARES   \n", "\n", "                                            file_path class_context stage  \n", "10  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...           NaN   NaN  \n", "11  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...           NaN   NaN  \n", "12  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...           NaN   NaN  \n", "15  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...           NaN   NaN  \n", "16  C:\\Shaik\\sample\\LineageTestProject\\AppOne\\User...           NaN   NaN  "]}, "execution_count": 220, "metadata": {}, "output_type": "execute_result"}], "source": ["df_final[df_final[\"source_type\"]==\"Class\"].head(5)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}