# ========== STAGE 3: LIGHTWEIGHT AST EXTRACTION ==========

def read_source_code(file_path):
    """Read source code from file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_ast_structure(file_path):
    """Extract AST structure from Java file using tree-sitter"""
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)

    def clean_node_name(name):
        """Clean node names to remove prefixes and suffixes"""
        if not name:
            return name
        
        # Remove common prefixes
        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):]
        
        # Apply PascalCase for consistency
        return to_pascal_case(name)

    def traverse(node, parent_name=None, parent_type=None):
        """Traverse AST and extract relationships"""
        node_type = node.type
        
        # Extract class declarations
        if node_type in ['class_declaration', 'interface_declaration']:
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    break
            
            if class_name:
                # File declares class
                records.append({
                    'source_node': extract_clean_name(file_name.replace('.java', ''), 'file'),
                    'source_type': 'File',
                    'destination_node': class_name,
                    'destination_type': 'Class',
                    'relationship': 'DECLARES',
                    'file_path': file_path
                })
                
                # Continue traversing with class as parent
                for child in node.children:
                    traverse(child, class_name, 'Class')
        
        # Extract method declarations
        elif node_type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    break
            
            if method_name and parent_name and parent_type == 'Class':
                # Class declares method
                records.append({
                    'source_node': parent_name,
                    'source_type': 'Class',
                    'destination_node': method_name,
                    'destination_type': 'Method',
                    'relationship': 'DECLARES',
                    'file_path': file_path
                })
                
                # Continue traversing with method as parent
                for child in node.children:
                    traverse(child, method_name, 'Method')
        
        # Extract field declarations
        elif node_type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            if field_name and parent_name and parent_type == 'Class':
                                # Class has field
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': 'Class',
                                    'destination_node': field_name,
                                    'destination_type': 'Variable',
                                    'relationship': 'HAS_FIELD',
                                    'file_path': file_path
                                })
        
        # Continue traversing for other nodes
        else:
            for child in node.children:
                traverse(child, parent_name, parent_type)

    traverse(root_node)
    return records

# Execute AST extraction
ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f'⚠️ Error processing {file}: {e}')
                continue

df_ast = pd.DataFrame(ast_records)
print(f'✅ Stage 3 Complete: {len(df_ast)} AST relationships extracted')

# Store AST results in memory
memory['ast_relationships'] = len(df_ast)
save_memory(memory)
