{"cells": [{"cell_type": "code", "execution_count": 1, "id": "stage1_configuration", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21472\\3669114846.py:37: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" Stage 1 Complete: Configuration loaded and Neo4j cleared\n"]}], "source": ["# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========\n", "import os\n", "import json\n", "import pickle\n", "import re\n", "import uuid\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "import threading\n", "\n", "# Tree-sitter for AST parsing\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "# LangChain components\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain.schema import Document\n", "\n", "# Configuration\n", "BASE_PATH = Path(r\"C:/Shaik/sample/LineageTestProject\")\n", "\n", "# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"v-8\"\n", "\n", "# Initialize connections\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# Azure OpenAI Configuration\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "# Temp variables to filter out (User Preference)\n", "TEMP_VARIABLES = {\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    'temp', 'tmp', 'temporary', 'temp1', 'temp2',\n", "    'count', 'counter', 'index', 'idx', 'iter',\n", "    'result', 'res', 'ret', 'val', 'value',\n", "    'item', 'elem', 'element', 'obj', 'object',\n", "    'str', 'string', 'num', 'number', 'flag',\n", "    'bool', 'boolean', 'arr', 'array', 'list',\n", "    'map', 'set', 'data', 'info', 'param', 'arg'\n", "}\n", "\n", "# Long-term memory storage\n", "MEMORY_FILE = \"lineage_memory_v8.pkl\"\n", "memory_lock = threading.Lock()\n", "\n", "def load_memory():\n", "    \"\"\"Load long-term memory from disk\"\"\"\n", "    try:\n", "        with open(MEMORY_FILE, 'rb') as f:\n", "            return pickle.load(f)\n", "    except FileNotFoundError:\n", "        return {\n", "            'class_registry': {},\n", "            'dto_mappings': {},\n", "            'validated_edges': set(),\n", "            'code_index': {},\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {}\n", "        }\n", "\n", "def save_memory(memory):\n", "    \"\"\"Save long-term memory to disk\"\"\"\n", "    with memory_lock:\n", "        with open(MEMORY_FILE, 'wb') as f:\n", "            pickle.dump(memory, f)\n", "\n", "# Initialize memory\n", "memory = load_memory()\n", "\n", "# Clear Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\" Stage 1 Complete: Configuration loaded and Neo4j cleared\")"]}, {"cell_type": "code", "execution_count": 2, "id": "utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Utility functions loaded\n"]}], "source": ["# ========== UTILITY FUNCTIONS ==========\n", "\n", "def to_pascal_case(text):\n", "    \"\"\"Convert text to PascalCase (User Preference)\"\"\"\n", "    if not text:\n", "        return text\n", "    \n", "    # Remove file extensions first\n", "    text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    def proper_capitalize(word):\n", "        \"\"\"Properly capitalize a word for PascalCase\"\"\"\n", "        if not word:\n", "            return word\n", "        \n", "        word_lower = word.lower()\n", "        \n", "        # Generic PascalCase conversion - capitalize first letter\n", "        return word_lower[0].upper() + word_lower[1:] if len(word_lower) > 1 else word_lower.upper()\n", "    \n", "    # Handle different input types\n", "    if '/' not in text and '\\\\' not in text and text.count('.') <= 1:\n", "        if '.' in text:\n", "            return text  # Handle method.variable format - don't change it\n", "        \n", "        if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):\n", "            return text  # Already in PascalCase\n", "        \n", "        parts = re.split(r'[_\\-\\s]+', text)\n", "        return ''.join(proper_capitalize(word) for word in parts if word)\n", "    \n", "    elif '/' in text or '\\\\' in text:\n", "        filename = os.path.basename(text)\n", "        filename = re.sub(r'\\.(java|class)$', '', filename, flags=re.IGNORECASE)\n", "        \n", "        if re.match(r'^[A-Z][a-zA-Z0-9]*$', filename):\n", "            return filename\n", "        \n", "        parts = re.split(r'[_\\-\\s]+', filename)\n", "        return ''.join(proper_capitalize(word) for word in parts if word)\n", "    \n", "    elif text.count('.') > 1:\n", "        parts = text.split('.')\n", "        main_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else text\n", "        name_parts = re.split(r'[_\\-\\s]+', main_name)\n", "        return ''.join(proper_capitalize(word) for word in name_parts if word)\n", "    \n", "    else:\n", "        parts = re.split(r'[_\\-\\s]+', text)\n", "        return ''.join(proper_capitalize(word) for word in parts if word)\n", "\n", "def is_temp_variable(var_name):\n", "    \"\"\"Check if variable is a common temp variable\"\"\"\n", "    if not var_name:\n", "        return True\n", "    var_lower = var_name.lower().strip()\n", "    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1\n", "\n", "def format_variable_with_method(method_name, var_name):\n", "    \"\"\"Format variable name with method context\"\"\"\n", "    if not method_name or not var_name:\n", "        return var_name\n", "    return f\"{to_pascal_case(method_name)}.{var_name}\"\n", "\n", "def extract_clean_name(full_name, name_type):\n", "    \"\"\"Extract clean name from potentially concatenated or path-like strings\"\"\"\n", "    if not full_name:\n", "        return full_name\n", "    \n", "    if name_type.lower() == 'method':\n", "        matches = re.findall(r'[a-z]+[A-Z][a-z]*|[A-Z][a-z]+', full_name)\n", "        if matches:\n", "            last_part = matches[-1]\n", "            if last_part[0].islower() or any(c.islower() for c in last_part[1:]):\n", "                return to_pascal_case(last_part)\n", "        \n", "        method_patterns = [\n", "            'calculate', 'get', 'set', 'create', 'update', 'delete', 'find', 'save', 'load',\n", "            'process', 'handle', 'execute', 'run', 'start', 'stop', 'init', 'build',\n", "            'parse', 'format', 'convert', 'transform', 'validate', 'check', 'verify',\n", "            'add', 'remove', 'insert', 'select', 'query', 'fetch', 'retrieve'\n", "        ]\n", "        for pattern in method_patterns:\n", "            if pattern.lower() in full_name.lower():\n", "                start_idx = full_name.lower().find(pattern.lower())\n", "                if start_idx >= 0:\n", "                    method_part = full_name[start_idx:]\n", "                    pattern_end = start_idx + len(pattern)\n", "                    if pattern_end < len(full_name):\n", "                        remaining = full_name[pattern_end:]\n", "                        match = re.search(r'^[a-z]*([A-Z][a-z]*)*', remaining)\n", "                        if match:\n", "                            method_part = pattern + match.group(0)\n", "                        else:\n", "                            method_part = pattern\n", "                    else:\n", "                        method_part = pattern\n", "                    return to_pascal_case(method_part)\n", "        \n", "        return to_pascal_case(full_name)\n", "    \n", "    elif name_type.lower() in ['class', 'file']:\n", "        if '/' in full_name or '\\\\' in full_name:\n", "            full_name = os.path.basename(full_name)\n", "        \n", "        full_name = re.sub(r'\\.(java|class)$', '', full_name, flags=re.IGNORECASE)\n", "        \n", "        if len(full_name) > 20:\n", "            matches = re.findall(r'[A-Z][a-z]+', full_name)\n", "            if matches:\n", "                meaningful_parts = matches[-2:] if len(matches) > 1 else matches[-1:]\n", "                return ''.join(meaningful_parts)\n", "        \n", "        return to_pascal_case(full_name)\n", "    \n", "    else:\n", "        return to_pascal_case(full_name)\n", "\n", "def normalize_node_name(name, node_type):\n", "    \"\"\"Normalize node names consistently across all stages\"\"\"\n", "    if not name:\n", "        return name\n", "    \n", "    # Remove common prefixes\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "    for prefix in prefixes:\n", "        if name.lower().startswith(prefix):\n", "            name = name[len(prefix):]\n", "    \n", "    name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "    \n", "    if node_type.lower() in ['folder', 'file', 'class', 'interface', 'method']:\n", "        name = extract_clean_name(name, node_type)\n", "    \n", "    elif node_type.lower() == 'variable':\n", "        if '.' in name:\n", "            parts = name.split('.')\n", "            if len(parts) >= 2:\n", "                context = extract_clean_name(parts[0], 'method')\n", "                var_name = '.'.join(parts[1:])\n", "                name = f\"{context}.{var_name}\"\n", "    \n", "    elif node_type.lower() == 'table':\n", "        name = to_pascal_case(name)\n", "    \n", "    elif node_type.lower() == 'endpoint':\n", "        if not re.match(r'^(GET|POST|PUT|DELETE|PATCH)\\s+/', name.upper()):\n", "            if not name.startswith('/'):\n", "                name = f\"/{name}\"\n", "            name = f\"GET {name}\"\n", "        else:\n", "            parts = name.split(' ', 1)\n", "            if len(parts) == 2:\n", "                method = parts[0].upper()\n", "                path = parts[1]\n", "                if not path.startswith('/'):\n", "                    path = f\"/{path}\"\n", "                name = f\"{method} {path}\"\n", "    \n", "    return name.strip()\n", "\n", "print(\"Utility functions loaded\")"]}, {"cell_type": "code", "execution_count": 3, "id": "variable_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Variable Registry initialized\n"]}], "source": ["# ========== VARIABLE METADATA REGISTRY ==========\n", "\n", "class VariableRegistry:\n", "    \"\"\"Registry to track variables across chunks with unique IDs and metadata\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.variables = {}  # var_id -> metadata\n", "        self.name_to_id = {}  # scoped_name -> var_id\n", "        self.chunk_memory = {}  # chunk_id -> variables seen\n", "        \n", "    def register_variable(self, scoped_name, chunk_id, context_info):\n", "        \"\"\"Register a variable with unique ID and metadata\"\"\"\n", "        if scoped_name in self.name_to_id:\n", "            var_id = self.name_to_id[scoped_name]\n", "            self.variables[var_id]['chunks'].add(chunk_id)\n", "            self.variables[var_id]['contexts'].append(context_info)\n", "        else:\n", "            var_id = f\"var_{uuid.uuid4().hex[:8]}\"\n", "            self.name_to_id[scoped_name] = var_id\n", "            self.variables[var_id] = {\n", "                'scoped_name': scoped_name,\n", "                'display_name': scoped_name.split('.')[-1] if '.' in scoped_name else scoped_name,\n", "                'scope_type': 'method' if '.' in scoped_name and not scoped_name.split('.')[0].endswith('Service') else 'class',\n", "                'scope_name': scoped_name.split('.')[0] if '.' in scoped_name else None,\n", "                'chunks': {chunk_id},\n", "                'contexts': [context_info],\n", "                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,\n", "                'modifications': [],\n", "                'usages': [],\n", "                'data_type': context_info.get('data_type'),\n", "                'lineage_path': []\n", "            }\n", "        \n", "        if chunk_id not in self.chunk_memory:\n", "            self.chunk_memory[chunk_id] = set()\n", "        self.chunk_memory[chunk_id].add(var_id)\n", "        \n", "        return var_id\n", "    \n", "    def add_variable_action(self, scoped_name, action, chunk_id, details):\n", "        \"\"\"Add an action (modification, usage) to a variable\"\"\"\n", "        var_id = self.name_to_id.get(scoped_name)\n", "        if var_id:\n", "            if action == 'modified':\n", "                self.variables[var_id]['modifications'].append({\n", "                    'chunk_id': chunk_id,\n", "                    'details': details\n", "                })\n", "            elif action == 'used':\n", "                self.variables[var_id]['usages'].append({\n", "                    'chunk_id': chunk_id,\n", "                    'details': details\n", "                })\n", "    \n", "    def get_variable_context(self, scoped_name):\n", "        \"\"\"Get full context for a variable for LLM processing\"\"\"\n", "        var_id = self.name_to_id.get(scoped_name)\n", "        if var_id:\n", "            return self.variables[var_id]\n", "        return None\n", "\n", "# Initialize global variable registry\n", "variable_registry = VariableRegistry()\n", "\n", "print(\" Variable Registry initialized\")"]}, {"cell_type": "code", "execution_count": 4, "id": "stage2_folder_file_hierarchy", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Stage 2 Complete: 6 folder/file relationships extracted\n", " Folders: 2, Files: 4\n"]}], "source": ["# ========== STAGE 2: FOLDER-FILE HIERARCHY ==========\n", "\n", "def extract_folder_file_hierarchy():\n", "    \"\"\"Extract and normalize folder-file relationships from BASE_PATH\"\"\"\n", "    relationships = []\n", "    base_folder = to_pascal_case(BASE_PATH.name)\n", "\n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        current_path = Path(root)\n", "        rel_path = current_path.relative_to(BASE_PATH)\n", "\n", "        # Determine current folder name and its parent\n", "        if rel_path != Path('.'):\n", "            folder_name = to_pascal_case(current_path.name)\n", "            parent_rel_path = current_path.parent.relative_to(BASE_PATH)\n", "            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)\n", "\n", "            relationships.append({\n", "                'source_node': normalize_node_name(parent_name, 'Folder'),\n", "                'source_type': 'Folder',\n", "                'destination_node': normalize_node_name(folder_name, 'Folder'),\n", "                'destination_type': 'Folder',\n", "                'relationship': 'CONTAINS'\n", "            })\n", "            current_folder_name = folder_name\n", "        else:\n", "            current_folder_name = base_folder\n", "\n", "        # Process files inside the folder\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_name = normalize_node_name(file, 'File')\n", "                relationships.append({\n", "                    'source_node': normalize_node_name(current_folder_name, 'Folder'),\n", "                    'source_type': 'Folder',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'File',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': str(current_path / file)\n", "                })\n", "\n", "    return relationships\n", "\n", "# Execute Stage 2\n", "folder_file_relationships = extract_folder_file_hierarchy()\n", "df_hierarchy = pd.DataFrame(folder_file_relationships)\n", "\n", "# Store Stage 2 results in memory\n", "memory['stage_2_results'] = {\n", "    'relationships': len(df_hierarchy),\n", "    'folders': len([r for r in folder_file_relationships if r['destination_type'] == 'Folder']),\n", "    'files': len([r for r in folder_file_relationships if r['destination_type'] == 'File'])\n", "}\n", "\n", "# Add validated edges to prevent duplicates\n", "for _, row in df_hierarchy.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "save_memory(memory)\n", "\n", "print(f\" Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted\")\n", "print(f\" Folders: {memory['stage_2_results']['folders']}, Files: {memory['stage_2_results']['files']}\")"]}, {"cell_type": "code", "execution_count": 5, "id": "stage2b_file_class", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Stage 2B Complete: 4 file-to-class relationships extracted\n", " Classes found: 4\n", " Class registry updated with 4 classes\n"]}], "source": ["# ========== STAGE 2B: FILE-TO-CLASS RELATIONSHIPS ==========\n", "\n", "def extract_file_class_relationships():\n", "    \"\"\"Extract explicit file-to-class relationships using regex pattern matching\"\"\"\n", "    relationships = []\n", "    class_registry = {}\n", "    \n", "    # Get all Java files from Stage 2\n", "    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']\n", "    \n", "    for _, file_row in java_files.iterrows():\n", "        file_path = file_row.get('file_path')\n", "        file_name = file_row['destination_node']\n", "        \n", "        if not file_path or not os.path.exists(file_path):\n", "            continue\n", "            \n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                source_code = f.read()\n", "            \n", "            # Extract class names using regex\n", "            class_pattern = r'(?:public\\s+)?(?:class|interface)\\s+(\\w+)'\n", "            class_matches = re.findall(class_pattern, source_code)\n", "            \n", "            for class_name in class_matches:\n", "                clean_class_name = extract_clean_name(class_name, 'class')\n", "                \n", "                # File declares Class\n", "                relationships.append({\n", "                    'source_node': file_name,\n", "                    'source_type': 'File',\n", "                    'destination_node': clean_class_name,\n", "                    'destination_type': 'Class',\n", "                    'relationship': 'DECLARES',\n", "                    'file_path': file_path\n", "                })\n", "                \n", "                # Update class registry with file information\n", "                if clean_class_name not in class_registry:\n", "                    class_registry[clean_class_name] = {}\n", "                \n", "                class_registry[clean_class_name].update({\n", "                    'file_path': file_path,\n", "                    'file_name': file_name,\n", "                    'source_code': source_code\n", "                })\n", "                \n", "        except Exception as e:\n", "            print(f\" Error processing file {file_name}: {e}\")\n", "            continue\n", "    \n", "    return relationships, class_registry\n", "\n", "# Execute Stage 2B\n", "file_class_relationships, class_registry = extract_file_class_relationships()\n", "df_file_class = pd.DataFrame(file_class_relationships)\n", "\n", "# Store Stage 2B results in memory\n", "memory['stage_2b_results'] = {\n", "    'relationships': len(df_file_class),\n", "    'classes_found': len([r for r in file_class_relationships if r['destination_type'] == 'Class'])\n", "}\n", "\n", "# Add validated edges\n", "for _, row in df_file_class.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "\n", "save_memory(memory)\n", "\n", "print(f\" Stage 2B Complete: {len(df_file_class)} file-to-class relationships extracted\")\n", "print(f\" Classes found: {memory['stage_2b_results']['classes_found']}\")\n", "print(f\" Class registry updated with {len(class_registry)} classes\")"]}, {"cell_type": "code", "execution_count": 6, "id": "stage3_class_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Found 3 endpoints in UserService\n", " Found 2 endpoints in OrderService\n", " Stage 3 Complete: Enhanced class registry built with 4 classes\n"]}], "source": ["# ========== STAGE 3: CLASS REGISTRY & ANALYSIS ==========\n", "\n", "# Patterns for analysis\n", "PACKAGE_PATTERN = r'package\\s+([\\w\\.]+);'\n", "IMPORT_PATTERN = r'import\\s+([\\w\\.]+);'\n", "MAPPING_PATTERNS = {\n", "    'GetMapping': r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PostMapping': r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PutMapping': r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'DeleteMapping': r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'RequestMapping': r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "}\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    \"\"\"Extract package name and import list from Java source\"\"\"\n", "    package_match = re.search(PACKAGE_PATTERN, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(IMPORT_PATTERN, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    \"\"\"Extract API endpoints using Spring annotations\"\"\"\n", "    endpoints = []\n", "    for mapping_type, pattern in MAPPING_PATTERNS.items():\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            path = match.strip()\n", "            if path:\n", "                method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                endpoints.append({\n", "                    'type': mapping_type,\n", "                    'path': path,\n", "                    'method': method\n", "                })\n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    \"\"\"Extract @Entity, @Table, and @Query usage from Java file\"\"\"\n", "    entities = []\n", "\n", "    # @Entity/@Table extraction\n", "    if \"@Entity\" in source_code_str:\n", "        table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "        for table_name in table_matches:\n", "            entities.append({'type': 'table', 'name': table_name.strip()})\n", "\n", "        if not table_matches:\n", "            class_match = re.search(r'(public\\s+)?(class|abstract class|interface)\\s+(\\w+)', source_code_str)\n", "            if class_match:\n", "                class_name = class_match.group(3)\n", "                snake_case = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                entities.append({'type': 'table', 'name': snake_case})\n", "\n", "    # @Query: detect raw SQL or JPQL references to tables\n", "    query_pattern = r'@Query\\s*\\([^)]*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']'\n", "    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "    for _, table in query_matches:\n", "        table = table.strip()\n", "        if table and table.lower() not in {'select', 'where', 'group', 'order'}:\n", "            entities.append({'type': 'table', 'name': table})\n", "\n", "    return entities\n", "\n", "def extract_interface_extends(source_code_str):\n", "    \"\"\"Extract interface extends and class implements relationships\"\"\"\n", "    extends_relationships = []\n", "    \n", "    # Interface extends\n", "    interface_extends_pattern = r'interface\\s+(\\w+)\\s+extends\\s+([\\w<>,\\s]+)'\n", "    matches = re.findall(interface_extends_pattern, source_code_str)\n", "    for interface_name, extends_clause in matches:\n", "        parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]\n", "        for parent in parent_interfaces:\n", "            if parent:\n", "                extends_relationships.append({\n", "                    'child_interface': to_pascal_case(interface_name),\n", "                    'parent_interface': to_pascal_case(parent),\n", "                    'full_extends': extends_clause.strip(),\n", "                    'type': 'interface_extends'\n", "                })\n", "    \n", "    # Class extends\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            extends_relationships.append({\n", "                'child_interface': to_pascal_case(child_class),\n", "                'parent_interface': to_pascal_case(parent_class),\n", "                'full_extends': parent_class,\n", "                'type': 'class_extends'\n", "            })\n", "    \n", "    # Class implements\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface:\n", "                extends_relationships.append({\n", "                    'child_interface': to_pascal_case(class_name),\n", "                    'parent_interface': to_pascal_case(interface),\n", "                    'full_extends': implements_clause.strip(),\n", "                    'type': 'class_implements'\n", "                })\n", "    \n", "    return extends_relationships\n", "\n", "def build_enhanced_class_registry():\n", "    \"\"\"Build enhanced class registry with API endpoints, DB entities, and code index\"\"\"\n", "    enhanced_registry = {}\n", "    code_index = {}\n", "    \n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    interface_extends = extract_interface_extends(source_code_str)\n", "                    \n", "                    class_name = to_pascal_case(file.replace('.java', ''))\n", "                    fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "                    \n", "                    enhanced_registry[class_name] = {\n", "                        'fqcn': fqcn,\n", "                        'package': package_name,\n", "                        'file_path': file_path,\n", "                        'imports': imports,\n", "                        'endpoints': endpoints,\n", "                        'db_entities': db_entities,\n", "                        'interface_extends': interface_extends,\n", "                        'source_code': source_code_str\n", "                    }\n", "                    \n", "                    # Build code index for fast method retrieval\n", "                    code_index[class_name] = {\n", "                        'methods': re.findall(r'(?:public|private|protected)\\s+\\w+\\s+(\\w+)\\s*\\(', source_code_str),\n", "                        'variables': re.findall(r'\\b(\\w+)\\s+(\\w+)\\s*[=;]', source_code_str),\n", "                        'annotations': re.findall(r'@(\\w+)', source_code_str)\n", "                    }\n", "                    \n", "                    if endpoints:\n", "                        print(f\" Found {len(endpoints)} endpoints in {class_name}\")\n", "                        \n", "                except Exception as e:\n", "                    print(f\" Error processing {file}: {e}\")\n", "                    continue\n", "    \n", "    # Store in long-term memory\n", "    memory['class_registry'] = enhanced_registry\n", "    memory['code_index'] = code_index\n", "    save_memory(memory)\n", "    \n", "    return enhanced_registry, code_index\n", "\n", "# Execute Stage 3\n", "enhanced_class_registry, code_index = build_enhanced_class_registry()\n", "print(f' Stage 3 Complete: Enhanced class registry built with {len(enhanced_class_registry)} classes')"]}, {"cell_type": "code", "execution_count": 7, "id": "stage4_ast_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Stage 4 Complete: 38 AST relationships extracted\n", " Classes: 4, Methods: 7, Variables: 22\n", " Endpoints: 5, Tables: 0\n"]}], "source": ["# ========== STAGE 4: AST STRUCTURE EXTRACTION ==========\n", "\n", "def read_source_code(file_path):\n", "    \"\"\"Read source code from file\"\"\"\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_ast_structure(file_path):\n", "    \"\"\"Extract AST structure for file-class-method-variable relationships\"\"\"\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = normalize_node_name(os.path.basename(file_path), 'File')\n", "\n", "    def clean_node_name(name):\n", "        if not name:\n", "            return name\n", "        name = re.sub(r'^(method:|class:|variable:|field:)', '', name, flags=re.IGNORECASE)\n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None):\n", "        node_type = node.type\n", "\n", "        # === CLASS DECLARATION ===\n", "        if node_type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                    class_name = normalize_node_name(clean_node_name(raw_name), 'Class')\n", "\n", "                    # File → Class\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'File',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'Class',\n", "                        'relationship': 'DECLARES',\n", "                        'file_path': file_path\n", "                    })\n", "\n", "                    # API Endpoints from Registry\n", "                    class_info = enhanced_class_registry.get(class_name, {})\n", "                    for ep in class_info.get('endpoints', []):\n", "                        endpoint = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'Class',\n", "                            'destination_node': endpoint,\n", "                            'destination_type': 'Endpoint',\n", "                            'relationship': 'DECLARES',\n", "                            'file_path': file_path\n", "                        })\n", "\n", "                    # DB Entities\n", "                    for entity in class_info.get('db_entities', []):\n", "                        table = normalize_node_name(entity['name'], 'Table')\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'Class',\n", "                            'destination_node': table,\n", "                            'destination_type': 'Table',\n", "                            'relationship': 'MAPS_TO',\n", "                            'file_path': file_path\n", "                        })\n", "                    \n", "                    # Add extends/implements relationships from registry\n", "                    interface_extends = class_info.get('interface_extends', [])\n", "                    for ext_rel in interface_extends:\n", "                        rel_type = 'EXTENDS' if ext_rel.get('type') == 'class_extends' else 'IMPLEMENTS'\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'Class',\n", "                            'destination_node': ext_rel['parent_interface'],\n", "                            'destination_type': 'Interface' if rel_type == 'IMPLEMENTS' else 'Class',\n", "                            'relationship': rel_type,\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'Class', class_name)\n", "\n", "        # === INTERFACE DECLARATION ===\n", "        elif node_type == 'interface_declaration':\n", "            interface_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                    interface_name = normalize_node_name(clean_node_name(raw_name), 'Interface')\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'File',\n", "                        'destination_node': interface_name,\n", "                        'destination_type': 'Interface',\n", "                        'relationship': 'DECLARES',\n", "                        'file_path': file_path\n", "                    })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'Interface', interface_name)\n", "\n", "        # === METHOD DECLARATION ===\n", "        elif node_type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                    method_name = normalize_node_name(clean_node_name(raw_name), 'Method')\n", "                    if parent_name and parent_type in ['Class', 'Interface']:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'Method',\n", "                            'relationship': 'DECLARES',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'Method', method_name)\n", "\n", "        # === FIELD DECLARATION (class-level) ===\n", "        elif node_type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            raw_name = source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8')\n", "                            if not is_temp_variable(raw_name) and parent_type == 'Class':\n", "                                full_var = f\"{parent_name}.{raw_name}\"\n", "                                formatted = normalize_node_name(full_var, 'Variable')\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': 'Class',\n", "                                    'destination_node': formatted,\n", "                                    'destination_type': 'Variable',\n", "                                    'relationship': 'HAS_FIELD',\n", "                                    'file_path': file_path,\n", "                                    'method_context': None\n", "                                })\n", "\n", "        # === METHOD-LEVEL VARIABLES ===\n", "        elif node_type in ['assignment_expression', 'variable_declarator'] and parent_type == 'Method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                    if not is_temp_variable(raw_name) and raw_name != 'this':\n", "                        try:\n", "                            formatted_var = normalize_node_name(format_variable_with_method(parent_name, raw_name), 'Variable')\n", "                            records.append({\n", "                                'source_node': parent_name,\n", "                                'source_type': 'Method',\n", "                                'destination_node': formatted_var,\n", "                                'destination_type': 'Variable',\n", "                                'relationship': 'USES',\n", "                                'file_path': file_path,\n", "                                'method_context': parent_name\n", "                            })\n", "                        except Exception:\n", "                            continue\n", "\n", "        # === GENERAL CASE: CONTINUE TRAVERSAL ===\n", "        for child in node.children:\n", "            traverse(child, parent_type, parent_name)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "# Execute AST Structure Extraction\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f' Error processing {file}: {e}')\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "\n", "# Store Stage 4 results in memory\n", "memory['stage_4_results'] = {\n", "    'relationships': len(df_ast),\n", "    'classes': len([r for r in ast_records if r['destination_type'] == 'Class']),\n", "    'methods': len([r for r in ast_records if r['destination_type'] == 'Method']),\n", "    'variables': len([r for r in ast_records if r['destination_type'] == 'Variable']),\n", "    'endpoints': len([r for r in ast_records if r['destination_type'] == 'Endpoint']),\n", "    'tables': len([r for r in ast_records if r['destination_type'] == 'Table'])\n", "}\n", "\n", "# Add validated edges and store method signatures\n", "for _, row in df_ast.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "    \n", "    # Store method signatures for cross-stage reference\n", "    if row['destination_type'] == 'Method':\n", "        memory['method_signatures'][row['destination_node']] = {\n", "            'class': row['source_node'],\n", "            'file_path': row.get('file_path', ''),\n", "            'stage': '4_ast'\n", "        }\n", "save_memory(memory)\n", "\n", "print(f' Stage 4 Complete: {len(df_ast)} AST relationships extracted')\n", "print(f' Classes: {memory[\"stage_4_results\"][\"classes\"]}, Methods: {memory[\"stage_4_results\"][\"methods\"]}, Variables: {memory[\"stage_4_results\"][\"variables\"]}')\n", "print(f' Endpoints: {memory[\"stage_4_results\"][\"endpoints\"]}, Tables: {memory[\"stage_4_results\"][\"tables\"]}')"]}, {"cell_type": "code", "execution_count": 8, "id": "5d751268", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 38 entries, 0 to 37\n", "Data columns (total 7 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   source_node       38 non-null     object\n", " 1   source_type       38 non-null     object\n", " 2   destination_node  38 non-null     object\n", " 3   destination_type  38 non-null     object\n", " 4   relationship      38 non-null     object\n", " 5   file_path         38 non-null     object\n", " 6   method_context    20 non-null     object\n", "dtypes: object(7)\n", "memory usage: 2.2+ KB\n"]}], "source": ["df_ast.info()"]}, {"cell_type": "code", "execution_count": 11, "id": "stage4b_llm_ast_context", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" UserService.java: 23 lines -> 1 chunk(s)\n", " UserUtils.java: 17 lines -> 1 chunk(s)\n", " OrderService.java: 23 lines -> 1 chunk(s)\n", " OrderUtils.java: 17 lines -> 1 chunk(s)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["🤖 Stage 4B: Enhanced LLM with AST Context: 100%|██████████| 4/4 [00:22<00:00,  5.59s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Stage 4B Complete: 32 LLM relationships extracted with AST context\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 4B: LLM EXTRACTION WITH AST CONTEXT ==========\n", "\n", "def build_enhanced_system_prompt(file_path, ast_df, class_registry):\n", "    \"\"\"Build enhanced system prompt with AST context and class registry\"\"\"\n", "    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = ''\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    \n", "    registry_context = 'Known Classes:\\n'\n", "    for class_name, info in class_registry.items():\n", "        registry_context += f'- {class_name} (FQCN: {info[\"fqcn\"]})\\n'\n", "        if len(info.get('endpoints', [])) > 0:\n", "            registry_context += f'  * {len(info[\"endpoints\"])} API endpoint(s)\\n'\n", "        if len(info.get('db_entities', [])) > 0:\n", "            registry_context += f'  * {len(info[\"db_entities\"])} DB entity/entities\\n'\n", "    \n", "    return f\"\"\"\n", "You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:\n", "\n", "CONTEXT:\n", "{registry_context}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):\n", "   - file -[declares]-> class\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable (for class fields: ClassName.fieldName)\n", "   - method -[uses]-> variable (for method variables: MethodName.variableName)\n", "   - class -[declares]-> endpoint\n", "   - class -[maps_to]-> table\n", "   - method -[calls]-> method\n", "   - method -[reads_from]-> table\n", "   - method -[writes_to]-> table\n", "   - class -[extends]-> class\n", "   - class -[implements]-> interface\n", "3. STRICT NAMING CONVENTIONS:\n", "   - Folders, Files, Classes, Methods: ALWAYS PascalCase (UserService, CreateUser)\n", "   - Variables: ALWAYS with context - MethodName.variableName OR ClassName.fieldName\n", "   - Endpoints: \"METHOD /path\" format (GET /api/users, POST /api/orders)\n", "   - Tables: PascalCase (UserTable, OrderTable)\n", "4. VARIABLE CONTEXT RULES:\n", "   - Method variables: CreateUser.userDto, GetUser.userId, SaveOrder.orderData\n", "   - Class fields: UserService.userRepository, OrderService.orderMapper\n", "   - NEVER create variables without context (just \"userDto\" is WRONG)\n", "5. ENDPOINT DETECTION:\n", "   - Look for @GetMapping, @PostMapping, @PutMapping, @DeleteMapping, @RequestMapping\n", "   - Extract path from annotation: @GetMapping(\"/users/{{id}}\") -> \"GET /users/{{id}}\"\n", "   - Connect to the class that declares them: UserController -[declares]-> \"GET /api/users\"\n", "6. Filter out temp variables like i, j, temp, tmp, counter, index, etc.\n", "7. Extract cross-class method calls and data flows\n", "8. Identify DTO ↔ Entity mappings and transformations\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "\n", "def smart_chunk_strategy(file_path, content):\n", "    \"\"\"Smart chunking: whole file if <1000 lines, language chunks if larger\"\"\"\n", "    lines = content.count('\\n') + 1\n", "    \n", "    if lines <= 1000:\n", "        return [{'content': content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]\n", "    else:\n", "        splitter = RecursiveCharacterTextSplitter.from_language(\n", "            language=LC_Language.JAVA,\n", "            chunk_size=8000,\n", "            chunk_overlap=400\n", "        )\n", "        doc = Document(page_content=content, metadata={'source': file_path})\n", "        chunks = splitter.split_documents([doc])\n", "        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]\n", "\n", "# Collect documents with smart chunking\n", "smart_docs = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                smart_docs.extend(smart_chunk_strategy(file_path, content))\n", "                print(f\" {file}: {content.count(chr(10)) + 1} lines -> {len(smart_chunk_strategy(file_path, content))} chunk(s)\")\n", "            except Exception as e:\n", "                print(f\" Error loading {file}: {e}\")\n", "                continue\n", "\n", "all_llm_lineage = []\n", "for doc_info in tqdm(smart_docs, desc='🤖 Stage 4B: Enhanced LLM with AST Context'):\n", "    file_path = doc_info['metadata'].get('source')\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint', 'column', 'dataset', 'operation', 'value'],\n", "        allowed_relationships=[\n", "            ('file', 'declares', 'class'),\n", "            ('file', 'declares', 'interface'),\n", "            ('class', 'declares', 'method'),\n", "            ('interface', 'declares', 'method'),\n", "            ('class', 'declares', 'endpoint'),\n", "            ('method', 'calls', 'method'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('class', 'uses', 'class'),\n", "            ('interface', 'extends', 'interface'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'maps_to', 'table'),\n", "            ('method', 'reads_from', 'table'),\n", "            ('method', 'writes_to', 'table'),\n", "            ('endpoint', 'reads_from', 'table'),\n", "            ('endpoint', 'writes_to', 'table'),\n", "            ('table', 'has_element', 'column'),\n", "            ('operation', 'reads_from', 'dataset'),\n", "            ('operation', 'writes_to', 'dataset')\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "    \n", "    try:\n", "        doc = Document(page_content=doc_info['content'], metadata=doc_info['metadata'])\n", "        graph_docs = transformer.convert_to_graph_documents([doc])\n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "\n", "                def normalize_entity(entity_name, entity_type):\n", "                    if not entity_name:\n", "                        return entity_name\n", "                    \n", "                    # Remove prefixes\n", "                    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "                    for prefix in prefixes:\n", "                        if entity_name.lower().startswith(prefix):\n", "                            entity_name = entity_name[len(prefix):]\n", "                    \n", "                    entity_name = re.sub(r'\\.(java|class)$', '', entity_name, flags=re.IGNORECASE)\n", "                    \n", "                    if entity_type in ['class', 'interface', 'method', 'file']:\n", "                        entity_name = extract_clean_name(entity_name, entity_type)\n", "                    elif entity_type == 'variable':\n", "                        if '.' in entity_name:\n", "                            parts = entity_name.split('.')\n", "                            context = extract_clean_name(parts[0], 'method')\n", "                            var_name = '.'.join(parts[1:])\n", "                            entity_name = f\"{context}.{var_name}\"\n", "                        else:\n", "                            current_file = file_path\n", "                            if current_file:\n", "                                class_name = extract_clean_name(os.path.basename(current_file).replace('.java', ''), 'class')\n", "                                entity_name = f\"{class_name}.{entity_name}\"\n", "                            else:\n", "                                return None\n", "                        \n", "                        var_part = entity_name.split('.')[-1] if '.' in entity_name else entity_name\n", "                        if is_temp_variable(var_part):\n", "                            return None\n", "                    elif entity_type == 'table':\n", "                        entity_name = to_pascal_case(entity_name)\n", "                    elif entity_type == 'endpoint':\n", "                        entity_name = normalize_node_name(entity_name, 'endpoint')\n", "                    \n", "                    return entity_name\n", "\n", "                s_node = normalize_entity(s_node, s_type)\n", "                t_node = normalize_entity(t_node, t_type)\n", "\n", "                if not s_node or not t_node or s_node == t_node:\n", "                    continue\n", "                \n", "                # Enforce correct relationship directions\n", "                valid_directions = {\n", "                    ('file', 'declares', 'class'),\n", "                    ('file', 'declares', 'interface'),\n", "                    ('class', 'declares', 'method'),\n", "                    ('interface', 'declares', 'method'),\n", "                    ('class', 'declares', 'endpoint'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'maps_to', 'table'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('interface', 'extends', 'interface'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                }\n", "                \n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type.title(),\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type.title(),\n", "                    'relationship': rel_type.upper(),\n", "                    'file_path': file_path\n", "                })\n", "                \n", "    except Exception:\n", "        continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "\n", "print(f'Stage 4B Complete: {len(df_llm_lineage)} LLM relationships extracted with AST context')"]}, {"cell_type": "code", "execution_count": null, "id": "stage5_llm_transformations", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 5: LLM-ENHANCED VARIABLE TRANSFORMATIONS ==========\n", "\n", "def extract_variable_transformations(class_registry, smart_docs):\n", "    \"\"\"Extract variable transformations using LLM analysis\"\"\"\n", "    relationships = []\n", "\n", "    for class_name, class_info in tqdm(class_registry.items(), desc=\"🤖 LLM Variable Analysis\"):\n", "        source_code = class_info['source_code']\n", "\n", "        try:\n", "            code_prompt = f\"\"\"\n", "You are a Java code analysis engine. Analyze this code and extract:\n", "\n", "1. VARIABLE TRANSFORMATIONS:\n", "   - FLOWS_TO: Direct data flow (dto.getName() → user.name)\n", "   - TRANSFORMS_TO: Data transformation (base.toUpperCase() → code)\n", "   - PRODUCES: Method produces result (price * quantity → total)\n", "\n", "2. DATABASE OPERATIONS:\n", "   - READS_FROM: Method reads from table\n", "   - WRITES_TO: Method writes to table\n", "\n", "3. METHOD CALLS:\n", "   - CALLS: Method calls another method\n", "\n", "RULES:\n", "- Use PascalCase for classes/methods\n", "- Keep original variable names\n", "- Use methodName.variableName format for variables\n", "- Filter out temp variables (i, j, temp, etc.)\n", "\n", "CODE:\n", "{source_code}\n", "\n", "Return relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Examples:\n", "[Variable]:CreateUser.userDto -[FLOWS_TO]-> [Variable]:CreateUser.user\n", "[Method]:CreateUser -[WRITES_TO]-> [Table]:User\n", "[Method]:GetUser -[CALLS]-> [Method]:FindById\n", "\"\"\"\n", "\n", "            response = llm.invoke(code_prompt)\n", "            content = response.content if hasattr(response, 'content') else str(response)\n", "\n", "            doc = Document(page_content=content, metadata={'class_name': class_name})\n", "\n", "            transformer = LLMGraphTransformer(\n", "                llm=llm,\n", "                allowed_nodes=['variable', 'method', 'table'],\n", "                allowed_relationships=[\n", "                    ('variable', 'flows_to', 'variable'),\n", "                    ('variable', 'transforms_to', 'variable'),\n", "                    ('method', 'produces', 'variable'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                ],\n", "                strict_mode=True,\n", "                node_properties=False,\n", "                relationship_properties=False,\n", "            )\n", "\n", "            graph_docs = transformer.convert_to_graph_documents([doc])\n", "\n", "            for gd in graph_docs:\n", "                for rel in gd.relationships:\n", "                    s_type = rel.source.type.title()\n", "                    t_type = rel.target.type.title()\n", "                    s_node = normalize_node_name(rel.source.id, s_type)\n", "                    t_node = normalize_node_name(rel.target.id, t_type)\n", "                    rel_type = rel.type.upper()\n", "\n", "                    # Filter out temp variables\n", "                    skip = False\n", "                    for node in [s_node, t_node]:\n", "                        if '.' in node:\n", "                            var_name = node.split('.')[-1]\n", "                            if is_temp_variable(var_name):\n", "                                skip = True\n", "                                break\n", "                    if skip:\n", "                        continue\n", "\n", "                    relationships.append({\n", "                        'source_node': s_node,\n", "                        'source_type': s_type,\n", "                        'destination_node': t_node,\n", "                        'destination_type': t_type,\n", "                        'relationship': rel_type,\n", "                        'class_context': class_name\n", "                    })\n", "\n", "                    # Add [USES] from Method → Variable\n", "                    for node_type, node_value in [(s_type, s_node), (t_type, t_node)]:\n", "                        if node_type == 'Variable' and '.' in node_value:\n", "                            method_part = node_value.split('.')[0]\n", "                            uses_rel = {\n", "                                'source_node': normalize_node_name(method_part, 'Method'),\n", "                                'source_type': 'Method',\n", "                                'destination_node': node_value,\n", "                                'destination_type': 'Variable',\n", "                                'relationship': 'USES',\n", "                                'class_context': class_name\n", "                            }\n", "                            if uses_rel not in relationships:\n", "                                relationships.append(uses_rel)\n", "\n", "        except Exception as llm_error:\n", "            print(f\" LLM error for {class_name}: {llm_error}\")\n", "            continue\n", "\n", "    return relationships\n", "\n", "# Run Stage 5\n", "transformation_relationships = extract_variable_transformations(enhanced_class_registry, smart_docs)\n", "df_transformations = pd.DataFrame(transformation_relationships)\n", "\n", "# Memory update\n", "memory['stage_5_results'] = {\n", "    'relationships': len(df_transformations),\n", "    'variable_transformations': len([r for r in transformation_relationships if 'TRANSFORMS_TO' in r.get('relationship', '')]),\n", "    'variable_flows': len([r for r in transformation_relationships if 'FLOWS_TO' in r.get('relationship', '')]),\n", "    'method_produces': len([r for r in transformation_relationships if 'PRODUCES' in r.get('relationship', '')]),\n", "    'db_operations': len([r for r in transformation_relationships if r.get('relationship') in ['READS_FROM', 'WRITES_TO']])\n", "}\n", "\n", "# Add to memory cache\n", "for rel in transformation_relationships:\n", "    edge_key = f\"{rel['source_node']}-{rel['relationship']}-{rel['destination_node']}\"\n", "    if edge_key not in memory['validated_edges']:\n", "        memory['validated_edges'].add(edge_key)\n", "\n", "        if rel['relationship'] in ['FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES']:\n", "            cache_key = f\"{rel['source_node']}_{rel['relationship']}\"\n", "            memory['transformation_cache'].setdefault(cache_key, []).append({\n", "                'target': rel['destination_node'],\n", "                'stage': '5',\n", "                'class_context': rel.get('class_context', '')\n", "            })\n", "\n", "        if rel['destination_type'] == 'Variable' and '.' in rel['destination_node']:\n", "            var_key = rel['destination_node']\n", "            memory['variable_flows'].setdefault(var_key, []).append({\n", "                'stage': '5',\n", "                'relationship': rel['relationship'],\n", "                'source': rel['source_node'],\n", "                'transformation_type': rel['relationship']\n", "            })\n", "\n", "save_memory(memory)\n", "\n", "print(f\" Stage 5 Complete: {len(df_transformations)} LLM transformation relationships extracted\")\n", "print(f\" Transformations: {memory['stage_5_results']['variable_transformations']}, Flows: {memory['stage_5_results']['variable_flows']}\")\n", "print(f\" Produces: {memory['stage_5_results']['method_produces']}, DB Ops: {memory['stage_5_results']['db_operations']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage6_data_consolidation", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 6: DATA CONSOLIDATION & NEO4J UPLOAD ==========\n", "\n", "def consolidate_all_relationships():\n", "    \"\"\"Consolidate all relationship data from different stages\"\"\"\n", "    all_relationships = []\n", "\n", "    print(f\"📊 Memory stats: {len(memory['validated_edges'])} validated edges, {len(memory['variable_flows'])} variable flows\")\n", "\n", "    # Stage 2: Folder-File relationships\n", "    for _, row in df_hierarchy.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'hierarchy'\n", "        })\n", "\n", "    # Stage 2B: File-Class relationships\n", "    for _, row in df_file_class.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'file_class'\n", "        })\n", "\n", "    # Stage 4B: LLM with AST Context\n", "    for _, row in df_llm_lineage.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'llm_ast_context'\n", "        })\n", "\n", "    # Stage 5: LLM variable transformations\n", "    for _, row in df_transformations.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'llm_transformations'\n", "        })\n", "    \n", "    df_combined = pd.DataFrame(all_relationships)\n", "    \n", "    # Remove duplicates\n", " \n", "    df_combined.drop_duplicates(\n", "        subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"],\n", "        inplace=True\n", "    )\n", "    \n", "    # Remove invalid relationships\n", "\n", "    file_to_class_declares = (\n", "        (df_combined['source_type'] == 'Folder') & \n", "        (df_combined['destination_type'] == 'File') & \n", "        (df_combined['relationship'] == 'CONTAINS')\n", "    ) | (\n", "        (df_combined['source_type'] == 'File') & \n", "        (df_combined['destination_type'] == 'Class') & \n", "        (df_combined['relationship'] == 'DECLARES')\n", "    )\n", "    \n", "    df_combined = df_combined[\n", "        (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &\n", "        (\n", "            (df_combined['source_node'] != df_combined['destination_node']) |\n", "            file_to_class_declares\n", "        ) &\n", "        (df_combined['source_node'].str.lower() != 'none') & \n", "        (df_combined['destination_node'].str.lower() != 'none') &\n", "        (df_combined['source_node'] != '') & \n", "        (df_combined['destination_node'] != '')\n", "    ]\n", "    \n", "    print(f\"Consolidation complete: {len(df_combined)} relationships\")\n", "    return df_combined\n", "\n", "def escape_label(label):\n", "    \"\"\"Escape labels that contain invalid characters for <PERSON><PERSON>\"\"\"\n", "    if not label.replace(\"_\", \"\").isalnum():\n", "        return f\"`{label}`\"\n", "    if label[0].isdigit():\n", "        return f\"`{label}`\"\n", "    return label\n", "\n", "def upload_to_neo4j(df_consolidated):\n", "    \"\"\"Upload consolidated lineage to Neo4j without colors\"\"\"\n", "    nodes_created = set()\n", "    \n", "    print(f\" Uploading {len(df_consolidated)} relationships to Neo4j...\")\n", "    \n", "    # Create Nodes without Colors\n", "    for _, row in tqdm(df_consolidated.iterrows(), desc=\"Creating Neo4j nodes\", total=len(df_consolidated)):\n", "        for node_name, node_type in [(row['source_node'], row['source_type']), (row['destination_node'], row['destination_type'])]:\n", "            if (node_name, node_type) not in nodes_created:\n", "                \n", "                if node_type.lower() == 'variable' and '.' in node_name:\n", "                    display_name = node_name.split('.')[-1]\n", "                    context = node_name.split('.')[0]\n", "                else:\n", "                    display_name = node_name\n", "                    context = None\n", "                \n", "                query = f\"\"\"\n", "                MERGE (n:{escape_label(node_type)} {{name: $name}})\n", "                SET n.display_name = $display_name,\n", "                    n.context = $context,\n", "                    n.node_type = $node_type\n", "                \"\"\"\n", "                try:\n", "                    graph.query(query, {\n", "                        'name': node_name,\n", "                        'display_name': display_name,\n", "                        'context': context,\n", "                        'node_type': node_type\n", "                    })\n", "                    nodes_created.add((node_name, node_type))\n", "                except Exception as e:\n", "                    print(f\" Node creation error [{node_name}, {node_type}]: {e}\")\n", "\n", "    # Create Relationships without Colors\n", "    relationships_created = 0\n", "    for _, row in tqdm(df_consolidated.iterrows(), desc=\"🔗 Creating Neo4j relationships\", total=len(df_consolidated)):\n", "        try:\n", "            query = f\"\"\"\n", "            MATCH (s:{escape_label(row['source_type'])} {{name: $source_name}})\n", "            MATCH (t:{escape_label(row['destination_type'])} {{name: $target_name}})\n", "            MERGE (s)-[r:{escape_label(row['relationship'])}]->(t)\n", "            SET r.stage = $stage\n", "            \"\"\"\n", "            graph.query(query, {\n", "                'source_name': row['source_node'],\n", "                'target_name': row['destination_node'],\n", "                'stage': row['stage']\n", "            })\n", "            relationships_created += 1\n", "        except Exception as e:\n", "            print(f\" Relationship creation error: {e}\")\n", "\n", "    return len(nodes_created), relationships_created\n", "\n", "# Execute Consolidation\n", "df_consolidated = consolidate_all_relationships()\n", "\n", "\n", "# Upload to Neo4j\n", "nodes_count, rels_count = upload_to_neo4j(df_consolidated)\n", "\n", "\n", "# Save CSV\n", "df_consolidated.to_csv('consolidated_lineage_v8.csv', index=False)\n", "print(f\"Stage 6C Complete: CSV saved to consolidated_lineage_v8.csv\")\n", "\n", "# Update Memory\n", "memory['final_stats'] = {\n", "    'total_relationships': len(df_consolidated),\n", "    'nodes_created': nodes_count,\n", "    'relationships_created': rels_count,\n", "    'stages_completed': 6\n", "}\n", "save_memory(memory)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}