# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========
import os
import json
import pickle
import re
import uuid
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Tree-sitter for AST parsing
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# LangChain components
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.schema import Document

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/LineageTestProject")

# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "v-8"

# Initialize connections
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

# Azure OpenAI Configuration
llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

# Temp variables to filter out (User Preference)
TEMP_VARIABLES = {
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    'temp', 'tmp', 'temporary', 'temp1', 'temp2',
    'count', 'counter', 'index', 'idx', 'iter',
    'result', 'res', 'ret', 'val', 'value',
    'item', 'elem', 'element', 'obj', 'object',
    'str', 'string', 'num', 'number', 'flag',
    'bool', 'boolean', 'arr', 'array', 'list',
    'map', 'set', 'data', 'info', 'param', 'arg'
}

# Long-term memory storage
MEMORY_FILE = "lineage_memory_v8.pkl"
memory_lock = threading.Lock()

def load_memory():
    """Load long-term memory from disk"""
    try:
        with open(MEMORY_FILE, 'rb') as f:
            return pickle.load(f)
    except FileNotFoundError:
        return {
            'class_registry': {},
            'dto_mappings': {},
            'validated_edges': set(),
            'code_index': {},
            'variable_flows': {},
            'method_signatures': {},
            'transformation_cache': {}
        }

def save_memory(memory):
    """Save long-term memory to disk"""
    with memory_lock:
        with open(MEMORY_FILE, 'wb') as f:
            pickle.dump(memory, f)

# Initialize memory
memory = load_memory()

# Clear Neo4j database
graph.query("MATCH (n) DETACH DELETE n")
print(" Stage 1 Complete: Configuration loaded and Neo4j cleared")

# ========== UTILITY FUNCTIONS ==========

def to_pascal_case(text):
    """Convert text to PascalCase (User Preference)"""
    if not text:
        return text
    
    # Remove file extensions first
    text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    def proper_capitalize(word):
        """Properly capitalize a word for PascalCase"""
        if not word:
            return word
        
        word_lower = word.lower()
        
        # Generic PascalCase conversion - capitalize first letter
        return word_lower[0].upper() + word_lower[1:] if len(word_lower) > 1 else word_lower.upper()
    
    # Handle different input types
    if '/' not in text and '\\' not in text and text.count('.') <= 1:
        if '.' in text:
            return text  # Handle method.variable format - don't change it
        
        if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):
            return text  # Already in PascalCase
        
        parts = re.split(r'[_\-\s]+', text)
        return ''.join(proper_capitalize(word) for word in parts if word)
    
    elif '/' in text or '\\' in text:
        filename = os.path.basename(text)
        filename = re.sub(r'\.(java|class)$', '', filename, flags=re.IGNORECASE)
        
        if re.match(r'^[A-Z][a-zA-Z0-9]*$', filename):
            return filename
        
        parts = re.split(r'[_\-\s]+', filename)
        return ''.join(proper_capitalize(word) for word in parts if word)
    
    elif text.count('.') > 1:
        parts = text.split('.')
        main_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else text
        name_parts = re.split(r'[_\-\s]+', main_name)
        return ''.join(proper_capitalize(word) for word in name_parts if word)
    
    else:
        parts = re.split(r'[_\-\s]+', text)
        return ''.join(proper_capitalize(word) for word in parts if word)

def is_temp_variable(var_name):
    """Check if variable is a common temp variable"""
    if not var_name:
        return True
    var_lower = var_name.lower().strip()
    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1

def format_variable_with_method(method_name, var_name):
    """Format variable name with method context"""
    if not method_name or not var_name:
        return var_name
    return f"{to_pascal_case(method_name)}.{var_name}"

def extract_clean_name(full_name, name_type):
    """Extract clean name from potentially concatenated or path-like strings"""
    if not full_name:
        return full_name
    
    if name_type.lower() == 'method':
        matches = re.findall(r'[a-z]+[A-Z][a-z]*|[A-Z][a-z]+', full_name)
        if matches:
            last_part = matches[-1]
            if last_part[0].islower() or any(c.islower() for c in last_part[1:]):
                return to_pascal_case(last_part)
        
        method_patterns = [
            'calculate', 'get', 'set', 'create', 'update', 'delete', 'find', 'save', 'load',
            'process', 'handle', 'execute', 'run', 'start', 'stop', 'init', 'build',
            'parse', 'format', 'convert', 'transform', 'validate', 'check', 'verify',
            'add', 'remove', 'insert', 'select', 'query', 'fetch', 'retrieve'
        ]
        for pattern in method_patterns:
            if pattern.lower() in full_name.lower():
                start_idx = full_name.lower().find(pattern.lower())
                if start_idx >= 0:
                    method_part = full_name[start_idx:]
                    pattern_end = start_idx + len(pattern)
                    if pattern_end < len(full_name):
                        remaining = full_name[pattern_end:]
                        match = re.search(r'^[a-z]*([A-Z][a-z]*)*', remaining)
                        if match:
                            method_part = pattern + match.group(0)
                        else:
                            method_part = pattern
                    else:
                        method_part = pattern
                    return to_pascal_case(method_part)
        
        return to_pascal_case(full_name)
    
    elif name_type.lower() in ['class', 'file']:
        if '/' in full_name or '\\' in full_name:
            full_name = os.path.basename(full_name)
        
        full_name = re.sub(r'\.(java|class)$', '', full_name, flags=re.IGNORECASE)
        
        if len(full_name) > 20:
            matches = re.findall(r'[A-Z][a-z]+', full_name)
            if matches:
                meaningful_parts = matches[-2:] if len(matches) > 1 else matches[-1:]
                return ''.join(meaningful_parts)
        
        return to_pascal_case(full_name)
    
    else:
        return to_pascal_case(full_name)

def normalize_node_name(name, node_type):
    """Normalize node names consistently across all stages"""
    if not name:
        return name
    
    # Remove common prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if name.lower().startswith(prefix):
            name = name[len(prefix):]
    
    name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
    
    if node_type.lower() in ['folder', 'file', 'class', 'interface', 'method']:
        name = extract_clean_name(name, node_type)
    
    elif node_type.lower() == 'variable':
        if '.' in name:
            parts = name.split('.')
            if len(parts) >= 2:
                context = extract_clean_name(parts[0], 'method')
                var_name = '.'.join(parts[1:])
                name = f"{context}.{var_name}"
    
    elif node_type.lower() == 'table':
        name = to_pascal_case(name)
    
    elif node_type.lower() == 'endpoint':
        if not re.match(r'^(GET|POST|PUT|DELETE|PATCH)\s+/', name.upper()):
            if not name.startswith('/'):
                name = f"/{name}"
            name = f"GET {name}"
        else:
            parts = name.split(' ', 1)
            if len(parts) == 2:
                method = parts[0].upper()
                path = parts[1]
                if not path.startswith('/'):
                    path = f"/{path}"
                name = f"{method} {path}"
    
    return name.strip()

print("Utility functions loaded")

# ========== VARIABLE METADATA REGISTRY ==========

class VariableRegistry:
    """Registry to track variables across chunks with unique IDs and metadata"""
    
    def __init__(self):
        self.variables = {}  # var_id -> metadata
        self.name_to_id = {}  # scoped_name -> var_id
        self.chunk_memory = {}  # chunk_id -> variables seen
        
    def register_variable(self, scoped_name, chunk_id, context_info):
        """Register a variable with unique ID and metadata"""
        if scoped_name in self.name_to_id:
            var_id = self.name_to_id[scoped_name]
            self.variables[var_id]['chunks'].add(chunk_id)
            self.variables[var_id]['contexts'].append(context_info)
        else:
            var_id = f"var_{uuid.uuid4().hex[:8]}"
            self.name_to_id[scoped_name] = var_id
            self.variables[var_id] = {
                'scoped_name': scoped_name,
                'display_name': scoped_name.split('.')[-1] if '.' in scoped_name else scoped_name,
                'scope_type': 'method' if '.' in scoped_name and not scoped_name.split('.')[0].endswith('Service') else 'class',
                'scope_name': scoped_name.split('.')[0] if '.' in scoped_name else None,
                'chunks': {chunk_id},
                'contexts': [context_info],
                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,
                'modifications': [],
                'usages': [],
                'data_type': context_info.get('data_type'),
                'lineage_path': []
            }
        
        if chunk_id not in self.chunk_memory:
            self.chunk_memory[chunk_id] = set()
        self.chunk_memory[chunk_id].add(var_id)
        
        return var_id
    
    def add_variable_action(self, scoped_name, action, chunk_id, details):
        """Add an action (modification, usage) to a variable"""
        var_id = self.name_to_id.get(scoped_name)
        if var_id:
            if action == 'modified':
                self.variables[var_id]['modifications'].append({
                    'chunk_id': chunk_id,
                    'details': details
                })
            elif action == 'used':
                self.variables[var_id]['usages'].append({
                    'chunk_id': chunk_id,
                    'details': details
                })
    
    def get_variable_context(self, scoped_name):
        """Get full context for a variable for LLM processing"""
        var_id = self.name_to_id.get(scoped_name)
        if var_id:
            return self.variables[var_id]
        return None

# Initialize global variable registry
variable_registry = VariableRegistry()

print(" Variable Registry initialized")

# ========== STAGE 2: FOLDER-FILE HIERARCHY ==========

def extract_folder_file_hierarchy():
    """Extract and normalize folder-file relationships from BASE_PATH"""
    relationships = []
    base_folder = to_pascal_case(BASE_PATH.name)

    for root, dirs, files in os.walk(BASE_PATH):
        current_path = Path(root)
        rel_path = current_path.relative_to(BASE_PATH)

        # Determine current folder name and its parent
        if rel_path != Path('.'):
            folder_name = to_pascal_case(current_path.name)
            parent_rel_path = current_path.parent.relative_to(BASE_PATH)
            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)

            relationships.append({
                'source_node': normalize_node_name(parent_name, 'Folder'),
                'source_type': 'Folder',
                'destination_node': normalize_node_name(folder_name, 'Folder'),
                'destination_type': 'Folder',
                'relationship': 'CONTAINS'
            })
            current_folder_name = folder_name
        else:
            current_folder_name = base_folder

        # Process files inside the folder
        for file in files:
            if file.lower().endswith(".java"):
                file_name = normalize_node_name(file, 'File')
                relationships.append({
                    'source_node': normalize_node_name(current_folder_name, 'Folder'),
                    'source_type': 'Folder',
                    'destination_node': file_name,
                    'destination_type': 'File',
                    'relationship': 'CONTAINS',
                    'file_path': str(current_path / file)
                })

    return relationships

# Execute Stage 2
folder_file_relationships = extract_folder_file_hierarchy()
df_hierarchy = pd.DataFrame(folder_file_relationships)

# Store Stage 2 results in memory
memory['stage_2_results'] = {
    'relationships': len(df_hierarchy),
    'folders': len([r for r in folder_file_relationships if r['destination_type'] == 'Folder']),
    'files': len([r for r in folder_file_relationships if r['destination_type'] == 'File'])
}

# Add validated edges to prevent duplicates
for _, row in df_hierarchy.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
save_memory(memory)

print(f" Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted")
print(f" Folders: {memory['stage_2_results']['folders']}, Files: {memory['stage_2_results']['files']}")

# ========== STAGE 2B: FILE-TO-CLASS RELATIONSHIPS ==========

def extract_file_class_relationships():
    """Extract explicit file-to-class relationships using regex pattern matching"""
    relationships = []
    class_registry = {}
    
    # Get all Java files from Stage 2
    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']
    
    for _, file_row in java_files.iterrows():
        file_path = file_row.get('file_path')
        file_name = file_row['destination_node']
        
        if not file_path or not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Extract class names using regex
            class_pattern = r'(?:public\s+)?(?:class|interface)\s+(\w+)'
            class_matches = re.findall(class_pattern, source_code)
            
            for class_name in class_matches:
                clean_class_name = extract_clean_name(class_name, 'class')
                
                # File declares Class
                relationships.append({
                    'source_node': file_name,
                    'source_type': 'File',
                    'destination_node': clean_class_name,
                    'destination_type': 'Class',
                    'relationship': 'DECLARES',
                    'file_path': file_path
                })
                
                # Update class registry with file information
                if clean_class_name not in class_registry:
                    class_registry[clean_class_name] = {}
                
                class_registry[clean_class_name].update({
                    'file_path': file_path,
                    'file_name': file_name,
                    'source_code': source_code
                })
                
        except Exception as e:
            print(f" Error processing file {file_name}: {e}")
            continue
    
    return relationships, class_registry

# Execute Stage 2B
file_class_relationships, class_registry = extract_file_class_relationships()
df_file_class = pd.DataFrame(file_class_relationships)

# Store Stage 2B results in memory
memory['stage_2b_results'] = {
    'relationships': len(df_file_class),
    'classes_found': len([r for r in file_class_relationships if r['destination_type'] == 'Class'])
}

# Add validated edges
for _, row in df_file_class.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)

save_memory(memory)

print(f" Stage 2B Complete: {len(df_file_class)} file-to-class relationships extracted")
print(f" Classes found: {memory['stage_2b_results']['classes_found']}")
print(f" Class registry updated with {len(class_registry)} classes")

# ========== STAGE 3: CLASS REGISTRY & ANALYSIS ==========

# Patterns for analysis
PACKAGE_PATTERN = r'package\s+([\w\.]+);'
IMPORT_PATTERN = r'import\s+([\w\.]+);'
MAPPING_PATTERNS = {
    'GetMapping': r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
    'PostMapping': r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
    'PutMapping': r'@PutMapping\s*\(\s*["\']([^"\']+)["\']',
    'DeleteMapping': r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']',
    'RequestMapping': r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']'
}

def extract_package_and_imports(source_code_str):
    """Extract package name and import list from Java source"""
    package_match = re.search(PACKAGE_PATTERN, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(IMPORT_PATTERN, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    """Extract API endpoints using Spring annotations"""
    endpoints = []
    for mapping_type, pattern in MAPPING_PATTERNS.items():
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            path = match.strip()
            if path:
                method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                endpoints.append({
                    'type': mapping_type,
                    'path': path,
                    'method': method
                })
    return endpoints

def extract_database_entities(source_code_str):
    """Extract @Entity, @Table, and @Query usage from Java file"""
    entities = []

    # @Entity/@Table extraction
    if "@Entity" in source_code_str:
        table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
        for table_name in table_matches:
            entities.append({'type': 'table', 'name': table_name.strip()})

        if not table_matches:
            class_match = re.search(r'(public\s+)?(class|abstract class|interface)\s+(\w+)', source_code_str)
            if class_match:
                class_name = class_match.group(3)
                snake_case = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
                entities.append({'type': 'table', 'name': snake_case})

    # @Query: detect raw SQL or JPQL references to tables
    query_pattern = r'@Query\s*\([^)]*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']'
    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)
    for _, table in query_matches:
        table = table.strip()
        if table and table.lower() not in {'select', 'where', 'group', 'order'}:
            entities.append({'type': 'table', 'name': table})

    return entities

def extract_interface_extends(source_code_str):
    """Extract interface extends and class implements relationships"""
    extends_relationships = []
    
    # Interface extends
    interface_extends_pattern = r'interface\s+(\w+)\s+extends\s+([\w<>,\s]+)'
    matches = re.findall(interface_extends_pattern, source_code_str)
    for interface_name, extends_clause in matches:
        parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]
        for parent in parent_interfaces:
            if parent:
                extends_relationships.append({
                    'child_interface': to_pascal_case(interface_name),
                    'parent_interface': to_pascal_case(parent),
                    'full_extends': extends_clause.strip(),
                    'type': 'interface_extends'
                })
    
    # Class extends
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            extends_relationships.append({
                'child_interface': to_pascal_case(child_class),
                'parent_interface': to_pascal_case(parent_class),
                'full_extends': parent_class,
                'type': 'class_extends'
            })
    
    # Class implements
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface:
                extends_relationships.append({
                    'child_interface': to_pascal_case(class_name),
                    'parent_interface': to_pascal_case(interface),
                    'full_extends': implements_clause.strip(),
                    'type': 'class_implements'
                })
    
    return extends_relationships

def build_enhanced_class_registry():
    """Build enhanced class registry with API endpoints, DB entities, and code index"""
    enhanced_registry = {}
    code_index = {}
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    package_name, imports = extract_package_and_imports(source_code_str)
                    endpoints = extract_api_endpoints(source_code_str)
                    db_entities = extract_database_entities(source_code_str)
                    interface_extends = extract_interface_extends(source_code_str)
                    
                    class_name = to_pascal_case(file.replace('.java', ''))
                    fqcn = f'{package_name}.{class_name}' if package_name else class_name
                    
                    enhanced_registry[class_name] = {
                        'fqcn': fqcn,
                        'package': package_name,
                        'file_path': file_path,
                        'imports': imports,
                        'endpoints': endpoints,
                        'db_entities': db_entities,
                        'interface_extends': interface_extends,
                        'source_code': source_code_str
                    }
                    
                    # Build code index for fast method retrieval
                    code_index[class_name] = {
                        'methods': re.findall(r'(?:public|private|protected)\s+\w+\s+(\w+)\s*\(', source_code_str),
                        'variables': re.findall(r'\b(\w+)\s+(\w+)\s*[=;]', source_code_str),
                        'annotations': re.findall(r'@(\w+)', source_code_str)
                    }
                    
                    if endpoints:
                        print(f" Found {len(endpoints)} endpoints in {class_name}")
                        
                except Exception as e:
                    print(f" Error processing {file}: {e}")
                    continue
    
    # Store in long-term memory
    memory['class_registry'] = enhanced_registry
    memory['code_index'] = code_index
    save_memory(memory)
    
    return enhanced_registry, code_index

# Execute Stage 3
enhanced_class_registry, code_index = build_enhanced_class_registry()
print(f' Stage 3 Complete: Enhanced class registry built with {len(enhanced_class_registry)} classes')

# ========== STAGE 4: AST STRUCTURE EXTRACTION ==========

def read_source_code(file_path):
    """Read source code from file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_ast_structure(file_path):
    """Extract AST structure for file-class-method-variable relationships"""
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = normalize_node_name(os.path.basename(file_path), 'File')

    def clean_node_name(name):
        if not name:
            return name
        name = re.sub(r'^(method:|class:|variable:|field:)', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
        return name.strip()

    def traverse(node, parent_type=None, parent_name=None):
        node_type = node.type

        # === CLASS DECLARATION ===
        if node_type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                    class_name = normalize_node_name(clean_node_name(raw_name), 'Class')

                    # File → Class
                    records.append({
                        'source_node': file_name,
                        'source_type': 'File',
                        'destination_node': class_name,
                        'destination_type': 'Class',
                        'relationship': 'DECLARES',
                        'file_path': file_path
                    })

                    # API Endpoints from Registry
                    class_info = enhanced_class_registry.get(class_name, {})
                    for ep in class_info.get('endpoints', []):
                        endpoint = f"{ep['method']} {ep['path']}"
                        records.append({
                            'source_node': class_name,
                            'source_type': 'Class',
                            'destination_node': endpoint,
                            'destination_type': 'Endpoint',
                            'relationship': 'DECLARES',
                            'file_path': file_path
                        })

                    # DB Entities
                    for entity in class_info.get('db_entities', []):
                        table = normalize_node_name(entity['name'], 'Table')
                        records.append({
                            'source_node': class_name,
                            'source_type': 'Class',
                            'destination_node': table,
                            'destination_type': 'Table',
                            'relationship': 'MAPS_TO',
                            'file_path': file_path
                        })
                    
                    # Add extends/implements relationships from registry
                    interface_extends = class_info.get('interface_extends', [])
                    for ext_rel in interface_extends:
                        rel_type = 'EXTENDS' if ext_rel.get('type') == 'class_extends' else 'IMPLEMENTS'
                        records.append({
                            'source_node': class_name,
                            'source_type': 'Class',
                            'destination_node': ext_rel['parent_interface'],
                            'destination_type': 'Interface' if rel_type == 'IMPLEMENTS' else 'Class',
                            'relationship': rel_type,
                            'file_path': file_path
                        })
                    break
            for child in node.children:
                traverse(child, 'Class', class_name)

        # === INTERFACE DECLARATION ===
        elif node_type == 'interface_declaration':
            interface_name = None
            for child in node.children:
                if child.type == 'identifier':
                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                    interface_name = normalize_node_name(clean_node_name(raw_name), 'Interface')
                    records.append({
                        'source_node': file_name,
                        'source_type': 'File',
                        'destination_node': interface_name,
                        'destination_type': 'Interface',
                        'relationship': 'DECLARES',
                        'file_path': file_path
                    })
                    break
            for child in node.children:
                traverse(child, 'Interface', interface_name)

        # === METHOD DECLARATION ===
        elif node_type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                    method_name = normalize_node_name(clean_node_name(raw_name), 'Method')
                    if parent_name and parent_type in ['Class', 'Interface']:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'Method',
                            'relationship': 'DECLARES',
                            'file_path': file_path
                        })
                    break
            for child in node.children:
                traverse(child, 'Method', method_name)

        # === FIELD DECLARATION (class-level) ===
        elif node_type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            raw_name = source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8')
                            if not is_temp_variable(raw_name) and parent_type == 'Class':
                                full_var = f"{parent_name}.{raw_name}"
                                formatted = normalize_node_name(full_var, 'Variable')
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': 'Class',
                                    'destination_node': formatted,
                                    'destination_type': 'Variable',
                                    'relationship': 'HAS_FIELD',
                                    'file_path': file_path,
                                    'method_context': None
                                })

        # === METHOD-LEVEL VARIABLES ===
        elif node_type in ['assignment_expression', 'variable_declarator'] and parent_type == 'Method':
            for child in node.children:
                if child.type == 'identifier':
                    raw_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                    if not is_temp_variable(raw_name) and raw_name != 'this':
                        try:
                            formatted_var = normalize_node_name(format_variable_with_method(parent_name, raw_name), 'Variable')
                            records.append({
                                'source_node': parent_name,
                                'source_type': 'Method',
                                'destination_node': formatted_var,
                                'destination_type': 'Variable',
                                'relationship': 'USES',
                                'file_path': file_path,
                                'method_context': parent_name
                            })
                        except Exception:
                            continue

        # === GENERAL CASE: CONTINUE TRAVERSAL ===
        for child in node.children:
            traverse(child, parent_type, parent_name)

    traverse(root_node)
    return records

# Execute AST Structure Extraction
ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f' Error processing {file}: {e}')

df_ast = pd.DataFrame(ast_records)

# Store Stage 4 results in memory
memory['stage_4_results'] = {
    'relationships': len(df_ast),
    'classes': len([r for r in ast_records if r['destination_type'] == 'Class']),
    'methods': len([r for r in ast_records if r['destination_type'] == 'Method']),
    'variables': len([r for r in ast_records if r['destination_type'] == 'Variable']),
    'endpoints': len([r for r in ast_records if r['destination_type'] == 'Endpoint']),
    'tables': len([r for r in ast_records if r['destination_type'] == 'Table'])
}

# Add validated edges and store method signatures
for _, row in df_ast.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
    
    # Store method signatures for cross-stage reference
    if row['destination_type'] == 'Method':
        memory['method_signatures'][row['destination_node']] = {
            'class': row['source_node'],
            'file_path': row.get('file_path', ''),
            'stage': '4_ast'
        }
save_memory(memory)

print(f' Stage 4 Complete: {len(df_ast)} AST relationships extracted')
print(f' Classes: {memory["stage_4_results"]["classes"]}, Methods: {memory["stage_4_results"]["methods"]}, Variables: {memory["stage_4_results"]["variables"]}')
print(f' Endpoints: {memory["stage_4_results"]["endpoints"]}, Tables: {memory["stage_4_results"]["tables"]}')

df_ast.info()

# ========== STAGE 4B: LLM EXTRACTION WITH AST CONTEXT ==========

def build_enhanced_system_prompt(file_path, ast_df, class_registry):
    """Build enhanced system prompt with AST context and class registry"""
    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()
    ast_context = ''
    for _, row in ast_subset.iterrows():
        ast_context += f"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\n"
    
    registry_context = 'Known Classes:\n'
    for class_name, info in class_registry.items():
        registry_context += f'- {class_name} (FQCN: {info["fqcn"]})\n'
        if len(info.get('endpoints', [])) > 0:
            registry_context += f'  * {len(info["endpoints"])} API endpoint(s)\n'
        if len(info.get('db_entities', [])) > 0:
            registry_context += f'  * {len(info["db_entities"])} DB entity/entities\n'
    
    return f"""
You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:

CONTEXT:
{registry_context}

AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):
{ast_context}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
   - file -[declares]-> class
   - class -[declares]-> method  
   - class -[has_field]-> variable (for class fields: ClassName.fieldName)
   - method -[uses]-> variable (for method variables: MethodName.variableName)
   - class -[declares]-> endpoint
   - class -[maps_to]-> table
   - method -[calls]-> method
   - method -[reads_from]-> table
   - method -[writes_to]-> table
   - class -[extends]-> class
   - class -[implements]-> interface
3. STRICT NAMING CONVENTIONS:
   - Folders, Files, Classes, Methods: ALWAYS PascalCase (UserService, CreateUser)
   - Variables: ALWAYS with context - MethodName.variableName OR ClassName.fieldName
   - Endpoints: "METHOD /path" format (GET /api/users, POST /api/orders)
   - Tables: PascalCase (UserTable, OrderTable)
4. VARIABLE CONTEXT RULES:
   - Method variables: CreateUser.userDto, GetUser.userId, SaveOrder.orderData
   - Class fields: UserService.userRepository, OrderService.orderMapper
   - NEVER create variables without context (just "userDto" is WRONG)
5. ENDPOINT DETECTION:
   - Look for @GetMapping, @PostMapping, @PutMapping, @DeleteMapping, @RequestMapping
   - Extract path from annotation: @GetMapping("/users/{{id}}") -> "GET /users/{{id}}"
   - Connect to the class that declares them: UserController -[declares]-> "GET /api/users"
6. Filter out temp variables like i, j, temp, tmp, counter, index, etc.
7. Extract cross-class method calls and data flows
8. Identify DTO ↔ Entity mappings and transformations

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""

def smart_chunk_strategy(file_path, content):
    """Smart chunking: whole file if <1000 lines, language chunks if larger"""
    lines = content.count('\n') + 1
    
    if lines <= 1000:
        return [{'content': content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]
    else:
        splitter = RecursiveCharacterTextSplitter.from_language(
            language=LC_Language.JAVA,
            chunk_size=8000,
            chunk_overlap=400
        )
        doc = Document(page_content=content, metadata={'source': file_path})
        chunks = splitter.split_documents([doc])
        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]

# Collect documents with smart chunking
smart_docs = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                smart_docs.extend(smart_chunk_strategy(file_path, content))
                print(f" {file}: {content.count(chr(10)) + 1} lines -> {len(smart_chunk_strategy(file_path, content))} chunk(s)")
            except Exception as e:
                print(f" Error loading {file}: {e}")
                continue

all_llm_lineage = []
for doc_info in tqdm(smart_docs, desc='🤖 Stage 4B: Enhanced LLM with AST Context'):
    file_path = doc_info['metadata'].get('source')
    
    transformer = LLMGraphTransformer(
        llm=llm,
        allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint', 'column', 'dataset', 'operation', 'value'],
        allowed_relationships=[
            ('file', 'declares', 'class'),
            ('file', 'declares', 'interface'),
            ('class', 'declares', 'method'),
            ('interface', 'declares', 'method'),
            ('class', 'declares', 'endpoint'),
            ('method', 'calls', 'method'),
            ('class', 'has_field', 'variable'),
            ('method', 'uses', 'variable'),
            ('class', 'uses', 'class'),
            ('interface', 'extends', 'interface'),
            ('class', 'extends', 'class'),
            ('class', 'implements', 'interface'),
            ('class', 'maps_to', 'table'),
            ('method', 'reads_from', 'table'),
            ('method', 'writes_to', 'table'),
            ('endpoint', 'reads_from', 'table'),
            ('endpoint', 'writes_to', 'table'),
            ('table', 'has_element', 'column'),
            ('operation', 'reads_from', 'dataset'),
            ('operation', 'writes_to', 'dataset')
        ],
        strict_mode=True,
        node_properties=False,
        relationship_properties=False,
    )
    
    try:
        doc = Document(page_content=doc_info['content'], metadata=doc_info['metadata'])
        graph_docs = transformer.convert_to_graph_documents([doc])
        for gd in graph_docs:
            for rel in gd.relationships:
                s_node = rel.source.id.strip()
                s_type = rel.source.type.strip().lower()
                t_node = rel.target.id.strip()
                t_type = rel.target.type.strip().lower()
                rel_type = rel.type.strip().lower()

                def normalize_entity(entity_name, entity_type):
                    if not entity_name:
                        return entity_name
                    
                    # Remove prefixes
                    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
                    for prefix in prefixes:
                        if entity_name.lower().startswith(prefix):
                            entity_name = entity_name[len(prefix):]
                    
                    entity_name = re.sub(r'\.(java|class)$', '', entity_name, flags=re.IGNORECASE)
                    
                    if entity_type in ['class', 'interface', 'method', 'file']:
                        entity_name = extract_clean_name(entity_name, entity_type)
                    elif entity_type == 'variable':
                        if '.' in entity_name:
                            parts = entity_name.split('.')
                            context = extract_clean_name(parts[0], 'method')
                            var_name = '.'.join(parts[1:])
                            entity_name = f"{context}.{var_name}"
                        else:
                            current_file = file_path
                            if current_file:
                                class_name = extract_clean_name(os.path.basename(current_file).replace('.java', ''), 'class')
                                entity_name = f"{class_name}.{entity_name}"
                            else:
                                return None
                        
                        var_part = entity_name.split('.')[-1] if '.' in entity_name else entity_name
                        if is_temp_variable(var_part):
                            return None
                    elif entity_type == 'table':
                        entity_name = to_pascal_case(entity_name)
                    elif entity_type == 'endpoint':
                        entity_name = normalize_node_name(entity_name, 'endpoint')
                    
                    return entity_name

                s_node = normalize_entity(s_node, s_type)
                t_node = normalize_entity(t_node, t_type)

                if not s_node or not t_node or s_node == t_node:
                    continue
                
                # Enforce correct relationship directions
                valid_directions = {
                    ('file', 'declares', 'class'),
                    ('file', 'declares', 'interface'),
                    ('class', 'declares', 'method'),
                    ('interface', 'declares', 'method'),
                    ('class', 'declares', 'endpoint'),
                    ('class', 'has_field', 'variable'),
                    ('method', 'uses', 'variable'),
                    ('class', 'maps_to', 'table'),
                    ('class', 'extends', 'class'),
                    ('class', 'implements', 'interface'),
                    ('interface', 'extends', 'interface'),
                    ('method', 'calls', 'method'),
                    ('method', 'reads_from', 'table'),
                    ('method', 'writes_to', 'table')
                }
                
                if (s_type, rel_type, t_type) not in valid_directions:
                    continue

                all_llm_lineage.append({
                    'source_node': s_node,
                    'source_type': s_type.title(),
                    'destination_node': t_node,
                    'destination_type': t_type.title(),
                    'relationship': rel_type.upper(),
                    'file_path': file_path
                })
                
    except Exception:
        continue

df_llm_lineage = pd.DataFrame(all_llm_lineage)

print(f'Stage 4B Complete: {len(df_llm_lineage)} LLM relationships extracted with AST context')

# ========== STAGE 5: LLM-ENHANCED VARIABLE TRANSFORMATIONS ==========

def extract_variable_transformations(class_registry, smart_docs):
    """Extract variable transformations using LLM analysis"""
    relationships = []

    for class_name, class_info in tqdm(class_registry.items(), desc="🤖 LLM Variable Analysis"):
        source_code = class_info['source_code']

        try:
            code_prompt = f"""
You are a Java code analysis engine. Analyze this code and extract:

1. VARIABLE TRANSFORMATIONS:
   - FLOWS_TO: Direct data flow (dto.getName() → user.name)
   - TRANSFORMS_TO: Data transformation (base.toUpperCase() → code)
   - PRODUCES: Method produces result (price * quantity → total)

2. DATABASE OPERATIONS:
   - READS_FROM: Method reads from table
   - WRITES_TO: Method writes to table

3. METHOD CALLS:
   - CALLS: Method calls another method

RULES:
- Use PascalCase for classes/methods
- Keep original variable names
- Use methodName.variableName format for variables
- Filter out temp variables (i, j, temp, etc.)

CODE:
{source_code}

Return relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Examples:
[Variable]:CreateUser.userDto -[FLOWS_TO]-> [Variable]:CreateUser.user
[Method]:CreateUser -[WRITES_TO]-> [Table]:User
[Method]:GetUser -[CALLS]-> [Method]:FindById
"""

            response = llm.invoke(code_prompt)
            content = response.content if hasattr(response, 'content') else str(response)

            doc = Document(page_content=content, metadata={'class_name': class_name})

            transformer = LLMGraphTransformer(
                llm=llm,
                allowed_nodes=['variable', 'method', 'table'],
                allowed_relationships=[
                    ('variable', 'flows_to', 'variable'),
                    ('variable', 'transforms_to', 'variable'),
                    ('method', 'produces', 'variable'),
                    ('method', 'reads_from', 'table'),
                    ('method', 'writes_to', 'table')
                ],
                strict_mode=True,
                node_properties=False,
                relationship_properties=False,
            )

            graph_docs = transformer.convert_to_graph_documents([doc])

            for gd in graph_docs:
                for rel in gd.relationships:
                    s_type = rel.source.type.title()
                    t_type = rel.target.type.title()
                    s_node = normalize_node_name(rel.source.id, s_type)
                    t_node = normalize_node_name(rel.target.id, t_type)
                    rel_type = rel.type.upper()

                    # Filter out temp variables
                    skip = False
                    for node in [s_node, t_node]:
                        if '.' in node:
                            var_name = node.split('.')[-1]
                            if is_temp_variable(var_name):
                                skip = True
                                break
                    if skip:
                        continue

                    relationships.append({
                        'source_node': s_node,
                        'source_type': s_type,
                        'destination_node': t_node,
                        'destination_type': t_type,
                        'relationship': rel_type,
                        'class_context': class_name
                    })

                    # Add [USES] from Method → Variable
                    for node_type, node_value in [(s_type, s_node), (t_type, t_node)]:
                        if node_type == 'Variable' and '.' in node_value:
                            method_part = node_value.split('.')[0]
                            uses_rel = {
                                'source_node': normalize_node_name(method_part, 'Method'),
                                'source_type': 'Method',
                                'destination_node': node_value,
                                'destination_type': 'Variable',
                                'relationship': 'USES',
                                'class_context': class_name
                            }
                            if uses_rel not in relationships:
                                relationships.append(uses_rel)

        except Exception as llm_error:
            print(f" LLM error for {class_name}: {llm_error}")
            continue

    return relationships

# Run Stage 5
transformation_relationships = extract_variable_transformations(enhanced_class_registry, smart_docs)
df_transformations = pd.DataFrame(transformation_relationships)

# Memory update
memory['stage_5_results'] = {
    'relationships': len(df_transformations),
    'variable_transformations': len([r for r in transformation_relationships if 'TRANSFORMS_TO' in r.get('relationship', '')]),
    'variable_flows': len([r for r in transformation_relationships if 'FLOWS_TO' in r.get('relationship', '')]),
    'method_produces': len([r for r in transformation_relationships if 'PRODUCES' in r.get('relationship', '')]),
    'db_operations': len([r for r in transformation_relationships if r.get('relationship') in ['READS_FROM', 'WRITES_TO']])
}

# Add to memory cache
for rel in transformation_relationships:
    edge_key = f"{rel['source_node']}-{rel['relationship']}-{rel['destination_node']}"
    if edge_key not in memory['validated_edges']:
        memory['validated_edges'].add(edge_key)

        if rel['relationship'] in ['FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES']:
            cache_key = f"{rel['source_node']}_{rel['relationship']}"
            memory['transformation_cache'].setdefault(cache_key, []).append({
                'target': rel['destination_node'],
                'stage': '5',
                'class_context': rel.get('class_context', '')
            })

        if rel['destination_type'] == 'Variable' and '.' in rel['destination_node']:
            var_key = rel['destination_node']
            memory['variable_flows'].setdefault(var_key, []).append({
                'stage': '5',
                'relationship': rel['relationship'],
                'source': rel['source_node'],
                'transformation_type': rel['relationship']
            })

save_memory(memory)

print(f" Stage 5 Complete: {len(df_transformations)} LLM transformation relationships extracted")
print(f" Transformations: {memory['stage_5_results']['variable_transformations']}, Flows: {memory['stage_5_results']['variable_flows']}")
print(f" Produces: {memory['stage_5_results']['method_produces']}, DB Ops: {memory['stage_5_results']['db_operations']}")

# ========== STAGE 6: DATA CONSOLIDATION & NEO4J UPLOAD ==========

def consolidate_all_relationships():
    """Consolidate all relationship data from different stages"""
    all_relationships = []

    print(f"📊 Memory stats: {len(memory['validated_edges'])} validated edges, {len(memory['variable_flows'])} variable flows")

    # Stage 2: Folder-File relationships
    for _, row in df_hierarchy.iterrows():
        all_relationships.append({
            'source_node': row['source_node'],
            'source_type': row['source_type'],
            'destination_node': row['destination_node'],
            'destination_type': row['destination_type'],
            'relationship': row['relationship'],
            'stage': 'hierarchy'
        })

    # Stage 2B: File-Class relationships
    for _, row in df_file_class.iterrows():
        all_relationships.append({
            'source_node': row['source_node'],
            'source_type': row['source_type'],
            'destination_node': row['destination_node'],
            'destination_type': row['destination_type'],
            'relationship': row['relationship'],
            'stage': 'file_class'
        })

    # Stage 4B: LLM with AST Context
    for _, row in df_llm_lineage.iterrows():
        all_relationships.append({
            'source_node': row['source_node'],
            'source_type': row['source_type'],
            'destination_node': row['destination_node'],
            'destination_type': row['destination_type'],
            'relationship': row['relationship'],
            'stage': 'llm_ast_context'
        })

    # Stage 5: LLM variable transformations
    for _, row in df_transformations.iterrows():
        all_relationships.append({
            'source_node': row['source_node'],
            'source_type': row['source_type'],
            'destination_node': row['destination_node'],
            'destination_type': row['destination_type'],
            'relationship': row['relationship'],
            'stage': 'llm_transformations'
        })
    
    df_combined = pd.DataFrame(all_relationships)
    
    # Remove duplicates
 
    df_combined.drop_duplicates(
        subset=["source_node", "source_type", "destination_node", "destination_type", "relationship"],
        inplace=True
    )
    
    # Remove invalid relationships

    file_to_class_declares = (
        (df_combined['source_type'] == 'Folder') & 
        (df_combined['destination_type'] == 'File') & 
        (df_combined['relationship'] == 'CONTAINS')
    ) | (
        (df_combined['source_type'] == 'File') & 
        (df_combined['destination_type'] == 'Class') & 
        (df_combined['relationship'] == 'DECLARES')
    )
    
    df_combined = df_combined[
        (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &
        (
            (df_combined['source_node'] != df_combined['destination_node']) |
            file_to_class_declares
        ) &
        (df_combined['source_node'].str.lower() != 'none') & 
        (df_combined['destination_node'].str.lower() != 'none') &
        (df_combined['source_node'] != '') & 
        (df_combined['destination_node'] != '')
    ]
    
    print(f"Consolidation complete: {len(df_combined)} relationships")
    return df_combined

def escape_label(label):
    """Escape labels that contain invalid characters for Cypher"""
    if not label.replace("_", "").isalnum():
        return f"`{label}`"
    if label[0].isdigit():
        return f"`{label}`"
    return label

def upload_to_neo4j(df_consolidated):
    """Upload consolidated lineage to Neo4j without colors"""
    nodes_created = set()
    
    print(f" Uploading {len(df_consolidated)} relationships to Neo4j...")
    
    # Create Nodes without Colors
    for _, row in tqdm(df_consolidated.iterrows(), desc="Creating Neo4j nodes", total=len(df_consolidated)):
        for node_name, node_type in [(row['source_node'], row['source_type']), (row['destination_node'], row['destination_type'])]:
            if (node_name, node_type) not in nodes_created:
                
                if node_type.lower() == 'variable' and '.' in node_name:
                    display_name = node_name.split('.')[-1]
                    context = node_name.split('.')[0]
                else:
                    display_name = node_name
                    context = None
                
                query = f"""
                MERGE (n:{escape_label(node_type)} {{name: $name}})
                SET n.display_name = $display_name,
                    n.context = $context,
                    n.node_type = $node_type
                """
                try:
                    graph.query(query, {
                        'name': node_name,
                        'display_name': display_name,
                        'context': context,
                        'node_type': node_type
                    })
                    nodes_created.add((node_name, node_type))
                except Exception as e:
                    print(f" Node creation error [{node_name}, {node_type}]: {e}")

    # Create Relationships without Colors
    relationships_created = 0
    for _, row in tqdm(df_consolidated.iterrows(), desc="🔗 Creating Neo4j relationships", total=len(df_consolidated)):
        try:
            query = f"""
            MATCH (s:{escape_label(row['source_type'])} {{name: $source_name}})
            MATCH (t:{escape_label(row['destination_type'])} {{name: $target_name}})
            MERGE (s)-[r:{escape_label(row['relationship'])}]->(t)
            SET r.stage = $stage
            """
            graph.query(query, {
                'source_name': row['source_node'],
                'target_name': row['destination_node'],
                'stage': row['stage']
            })
            relationships_created += 1
        except Exception as e:
            print(f" Relationship creation error: {e}")

    return len(nodes_created), relationships_created

# Execute Consolidation
df_consolidated = consolidate_all_relationships()


# Upload to Neo4j
nodes_count, rels_count = upload_to_neo4j(df_consolidated)


# Save CSV
df_consolidated.to_csv('consolidated_lineage_v8.csv', index=False)
print(f"Stage 6C Complete: CSV saved to consolidated_lineage_v8.csv")

# Update Memory
memory['final_stats'] = {
    'total_relationships': len(df_consolidated),
    'nodes_created': nodes_count,
    'relationships_created': rels_count,
    'stages_completed': 6
}
save_memory(memory)

