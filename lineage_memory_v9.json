{"class_registry": {"UserService": {"fqcn": "AppOne.UserService", "package": "AppOne", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java", "imports": [], "endpoints": [{"type": "GetMapping", "path": "/get/{id}", "method": "GET"}, {"type": "PostMapping", "path": "/create", "method": "POST"}, {"type": "RequestMapping", "path": "/user", "method": "GET"}], "db_entities": [], "source_code": "package AppOne;\n\nimport org.springframework.web.bind.annotation.*;\n\n@RestController\n@RequestMapping(\"/user\")\npublic class UserService {\n\n    private UserRepository userRepository = new UserRepository();\n\n    @PostMapping(\"/create\")\n    public User createUser(@RequestBody UserDto userDto) {\n        User user = UserUtils.convertDtoToEntity(userDto);\n        user.setStatus(\"ACTIVE\");\n        userRepository.save(user); // writes to User table\n        return user;\n    }\n\n    @GetMapping(\"/get/{id}\")\n    public User getUser(@PathVariable int id) {\n        return userRepository.findById(id); // reads from User table\n    }\n}"}, "UserUtils": {"fqcn": "AppOne.UserUtils", "package": "AppOne", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserUtils.java", "imports": [], "endpoints": [], "db_entities": [], "source_code": "package AppOne;\n\npublic class UserUtils {\n\n    public static User convertDtoToEntity(UserDto dto) {\n        User user = new User();\n        user.setName(dto.getName()); // dto.name FLOWS_TO user.name\n        user.setEmail(dto.getEmail()); // dto.email FLOWS_TO user.email\n        return user;\n    }\n\n    public static String generateUserCode(User user) {\n        String base = user.getName() + \"-\" + user.getEmail(); // user.name + user.email FLOWS_TO base\n        String code = base.toUpperCase(); // base TRANSFORMS_TO code\n        return code; // code PRODUCES output\n    }\n}"}, "OrderService": {"fqcn": "AppTwo.OrderService", "package": "AppTwo", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderService.java", "imports": ["AppOne.User"], "endpoints": [{"type": "PostMapping", "path": "/create", "method": "POST"}, {"type": "RequestMapping", "path": "/order", "method": "GET"}], "db_entities": [], "source_code": "package AppTwo;\n\nimport org.springframework.web.bind.annotation.*;\nimport AppOne.User;\n\n@RestController\n@RequestMapping(\"/order\")\npublic class OrderService {\n\n    private OrderRepository orderRepository = new OrderRepository();\n\n    @PostMapping(\"/create\")\n    public Order createOrder(@RequestBody OrderDto orderDto) {\n        Order order = OrderUtils.convertDtoToEntity(orderDto);\n        order.setStatus(\"PENDING\");\n        orderRepository.save(order); // writes to Order table\n\n        User user = orderDto.getUser(); // retrieves User DTO reference\n        String userCode = AppOne.UserUtils.generateUserCode(user); // calls AppOne utility (cross-app)\n        order.setUserCode(userCode); // userCode FLOWS_TO order.userCode\n        return order;\n    }\n}"}, "OrderUtils": {"fqcn": "AppTwo.OrderUtils", "package": "AppTwo", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderUtils.java", "imports": [], "endpoints": [], "db_entities": [], "source_code": "package AppTwo;\n\npublic class OrderUtils {\n\n    public static Order convertDtoToEntity(OrderDto dto) {\n        Order order = new Order();\n        order.setItem(dto.getItem()); // dto.item FLOWS_TO order.item\n        order.setQuantity(dto.getQuantity()); // dto.quantity FLOWS_TO order.quantity\n        return order;\n    }\n\n    public static double calculateTotal(double price, int quantity) {\n        double total = price * quantity; // price, quantity FLOWS_TO total\n        double taxedTotal = total * 1.18; // total TRANSFORMS_TO taxedTotal\n        return taxedTotal; // taxedTotal PRODUCES output\n    }\n}"}}, "validated_edges": ["Getuser-USES-Userrepository", "Orderservice-DECLARES-Createorder", "Userutils-DECLARES-Generateusercode", "Orderutils-DECLARES-Convertdtotoentity", "LineageTestProject-CONTAINS-AppTwo", "Userservice-HAS_FIELD-Userrepository", "UserUtils-DECLARES-UserUtils", "UserService-DECLARES-UserService", "Createorder-USES-Orderrepository", "OrderService-DECLARES-OrderService", "Userservice-DECLARES-/Create", "Convertdtotoentity-USES-Order", "Calculatetotal-USES-Total", "Orderservice-HAS_FIELD-Orderrepository", "LineageTestProject-CONTAINS-AppOne", "Calculatetotal-USES-Price", "Convertdtotoentity-USES-User", "Generateusercode-USES-Base", "Orderutils-DECLARES-Calculatetotal", "Userservice-MAPS_TO-User", "AppTwo-CONTAINS-OrderUtils", "Userservice-DECLARES-Createuser", "AppOne-CONTAINS-UserService", "Calculatetotal-USES-Taxedtotal", "Userservice-DECLARES-Getuser", "Orderservice-MAPS_TO-Order", "Createuser-USES-Userrepository", "Userutils-DECLARES-Convertdtotoentity", "AppOne-CONTAINS-UserUtils", "Userservice-DECLARES-/Get/{Id}", "Generateusercode-USES-Code", "Convertdtotoentity-USES-Dto", "OrderUtils-DECLARES-OrderUtils", "Calculatetotal-USES-Quantity", "AppTwo-CONTAINS-OrderService"], "variable_contexts": {"Userrepository": {"context": "<PERSON><PERSON>", "context_type": "method", "stage": "4b_structural"}, "User": {"context": "Convertdtotoentity", "context_type": "method", "stage": "4b_structural"}, "Base": {"context": "Generateusercode", "context_type": "method", "stage": "4b_structural"}, "Code": {"context": "Generateusercode", "context_type": "method", "stage": "4b_structural"}, "Orderrepository": {"context": "Createorder", "context_type": "method", "stage": "4b_structural"}, "Dto": {"context": "Convertdtotoentity", "context_type": "method", "stage": "4b_structural"}, "Order": {"context": "Convertdtotoentity", "context_type": "method", "stage": "4b_structural"}, "Price": {"context": "Calculatetotal", "context_type": "method", "stage": "4b_structural"}, "Quantity": {"context": "Calculatetotal", "context_type": "method", "stage": "4b_structural"}, "Total": {"context": "Calculatetotal", "context_type": "method", "stage": "4b_structural"}, "Taxedtotal": {"context": "Calculatetotal", "context_type": "method", "stage": "4b_structural"}}, "method_signatures": {"CreateUser": {"class": "UserService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java"}, "GetUser": {"class": "UserService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java"}, "CreateOrder": {"class": "OrderService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderService.java"}}}